import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { profileAPI } from '../services/api';

const ProfileScreen = ({ navigation }) => {
  const { user, logout } = useAuth();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const response = await profileAPI.getProfile();
      if (response.success) {
        setProfile(response.data);
      }
    } catch (error) {
      console.error('Profile fetch error:', error);
      Alert.alert('Error', 'Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  const menuItems = [
    {
      id: 'personal',
      title: 'Personal Information',
      icon: 'person-outline',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon'),
    },
    {
      id: 'banking',
      title: 'Banking Information',
      icon: 'card-outline',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon'),
    },
    {
      id: 'security',
      title: 'Security & PIN',
      icon: 'shield-outline',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon'),
    },
    {
      id: 'notifications',
      title: 'Notification Settings',
      icon: 'notifications-outline',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon'),
    },
    {
      id: 'documents',
      title: 'Documents',
      icon: 'document-outline',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon'),
    },
    {
      id: 'support',
      title: 'Help & Support',
      icon: 'help-circle-outline',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon'),
    },
    {
      id: 'about',
      title: 'About Lend Flow',
      icon: 'information-circle-outline',
      onPress: () => Alert.alert('About', 'Lend Flow v1.0.0\nYour trusted loan partner'),
    },
  ];

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2E7D32" />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.profileSection}>
            <View style={styles.avatarContainer}>
              <Ionicons name="person" size={40} color="#2E7D32" />
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>
                {profile?.firstName || 'User'} {profile?.lastName || ''}
              </Text>
              <Text style={styles.userPhone}>{profile?.phoneNumber}</Text>
              <Text style={styles.userEmail}>{profile?.email}</Text>
            </View>
          </View>
          
          {/* Verification Status */}
          <View style={styles.verificationContainer}>
            <View style={[
              styles.verificationBadge,
              { backgroundColor: profile?.isVerified ? '#4CAF50' : '#FF9800' }
            ]}>
              <Ionicons
                name={profile?.isVerified ? 'checkmark-circle' : 'time'}
                size={16}
                color="#fff"
              />
              <Text style={styles.verificationText}>
                {profile?.isVerified ? 'Verified' : 'Pending Verification'}
              </Text>
            </View>
          </View>
        </View>

        {/* Credit Score */}
        {profile?.creditScore && (
          <View style={styles.creditScoreContainer}>
            <View style={styles.creditScoreCard}>
              <View style={styles.creditScoreHeader}>
                <Ionicons name="trending-up" size={24} color="#2E7D32" />
                <Text style={styles.creditScoreTitle}>Credit Score</Text>
              </View>
              <Text style={styles.creditScoreValue}>{profile.creditScore}</Text>
              <Text style={styles.creditScoreRange}>300 - 850</Text>
              <View style={styles.creditScoreBar}>
                <View
                  style={[
                    styles.creditScoreFill,
                    { width: `${((profile.creditScore - 300) / 550) * 100}%` }
                  ]}
                />
              </View>
            </View>
          </View>
        )}

        {/* Banking Information */}
        {profile?.bankingInfo && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Banking Information</Text>
            <View style={styles.bankingCard}>
              <View style={styles.bankingHeader}>
                <Ionicons name="card" size={20} color="#2E7D32" />
                <Text style={styles.bankingTitle}>{profile.bankingInfo.bankName}</Text>
              </View>
              <Text style={styles.bankingDetail}>
                Account: ****{profile.bankingInfo.accountNumber.slice(-4)}
              </Text>
              <Text style={styles.bankingDetail}>
                Type: {profile.bankingInfo.accountType}
              </Text>
              <Text style={styles.bankingDetail}>
                Holder: {profile.bankingInfo.accountHolderName}
              </Text>
            </View>
          </View>
        )}

        {/* Menu Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.menuItem}
              onPress={item.onPress}
            >
              <View style={styles.menuItemLeft}>
                <Ionicons name={item.icon} size={24} color="#666" />
                <Text style={styles.menuItemText}>{item.title}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#666" />
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Button */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Ionicons name="log-out-outline" size={24} color="#f44336" />
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>

        {/* App Version */}
        <View style={styles.footer}>
          <Text style={styles.versionText}>Lend Flow v1.0.0</Text>
          <Text style={styles.copyrightText}>© 2024 Lend Flow. All rights reserved.</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#E8F5E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userPhone: {
    fontSize: 16,
    color: '#666',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  verificationContainer: {
    alignItems: 'flex-start',
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  verificationText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  creditScoreContainer: {
    padding: 20,
  },
  creditScoreCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  creditScoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  creditScoreTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  creditScoreValue: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#2E7D32',
    textAlign: 'center',
    marginBottom: 8,
  },
  creditScoreRange: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  creditScoreBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
  },
  creditScoreFill: {
    height: '100%',
    backgroundColor: '#2E7D32',
    borderRadius: 4,
  },
  section: {
    padding: 20,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  bankingCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  bankingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  bankingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  bankingDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  menuItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 16,
  },
  logoutButton: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#f44336',
  },
  logoutText: {
    fontSize: 16,
    color: '#f44336',
    fontWeight: '600',
    marginLeft: 8,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  versionText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  copyrightText: {
    fontSize: 12,
    color: '#999',
  },
});

export default ProfileScreen;
