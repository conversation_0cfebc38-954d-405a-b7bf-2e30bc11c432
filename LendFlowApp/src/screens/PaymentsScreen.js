import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { paymentsAPI, loansAPI, formatCurrency, formatDate } from '../services/api';

const PaymentsScreen = ({ navigation }) => {
  const [payments, setPayments] = useState([]);
  const [paymentSummary, setPaymentSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchPaymentsData = useCallback(async () => {
    try {
      const [paymentsRes, summaryRes] = await Promise.all([
        paymentsAPI.getPayments(),
        paymentsAPI.getPaymentSummary(),
      ]);

      if (paymentsRes.success) {
        setPayments(paymentsRes.data);
      }

      if (summaryRes.success) {
        setPaymentSummary(summaryRes.data);
      }
    } catch (error) {
      console.error('Payments fetch error:', error);
      Alert.alert('Error', 'Failed to load payments');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    fetchPaymentsData();
  }, [fetchPaymentsData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchPaymentsData();
  }, [fetchPaymentsData]);

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#4CAF50';
      case 'pending':
        return '#FF9800';
      case 'failed':
        return '#f44336';
      default:
        return '#666';
    }
  };

  const getPaymentStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'pending':
        return 'time';
      case 'failed':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const renderPaymentCard = (payment) => (
    <View key={payment.id} style={styles.paymentCard}>
      <View style={styles.paymentHeader}>
        <View style={styles.paymentInfo}>
          <Text style={styles.paymentAmount}>
            {formatCurrency(payment.amount)}
          </Text>
          <Text style={styles.paymentDate}>
            {formatDate(payment.paymentDate)}
          </Text>
        </View>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getPaymentStatusColor(payment.status) },
          ]}
        >
          <Ionicons
            name={getPaymentStatusIcon(payment.status)}
            size={12}
            color="#fff"
          />
          <Text style={styles.statusText}>
            {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
          </Text>
        </View>
      </View>

      <View style={styles.paymentDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Loan ID:</Text>
          <Text style={styles.detailValue}>{payment.loanId.slice(-8)}</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Due Date:</Text>
          <Text style={styles.detailValue}>
            {formatDate(payment.dueDate)}
          </Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Method:</Text>
          <Text style={styles.detailValue}>
            {payment.paymentMethod === 'bank_transfer' ? 'Bank Transfer' : payment.paymentMethod}
          </Text>
        </View>
      </View>

      {payment.status === 'pending' && (
        <TouchableOpacity style={styles.payNowButton}>
          <Text style={styles.payNowText}>Pay Now</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2E7D32" />
          <Text style={styles.loadingText}>Loading payments...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.title}>Payments</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Payment Summary */}
        {paymentSummary && (
          <View style={styles.summaryContainer}>
            <Text style={styles.summaryTitle}>Payment Summary</Text>
            
            <View style={styles.summaryCards}>
              <View style={styles.summaryCard}>
                <Ionicons name="card" size={24} color="#4CAF50" />
                <Text style={styles.summaryValue}>
                  {formatCurrency(paymentSummary.totalAmountPaid)}
                </Text>
                <Text style={styles.summaryLabel}>Total Paid</Text>
              </View>
              
              <View style={styles.summaryCard}>
                <Ionicons name="list" size={24} color="#2196F3" />
                <Text style={styles.summaryValue}>
                  {paymentSummary.totalPayments}
                </Text>
                <Text style={styles.summaryLabel}>Total Payments</Text>
              </View>
              
              <View style={styles.summaryCard}>
                <Ionicons name="time" size={24} color="#FF9800" />
                <Text style={styles.summaryValue}>
                  {paymentSummary.pendingPayments}
                </Text>
                <Text style={styles.summaryLabel}>Pending</Text>
              </View>
            </View>
          </View>
        )}

        {/* Recent Payments */}
        {paymentSummary?.recentPayments && paymentSummary.recentPayments.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Payments</Text>
            {paymentSummary.recentPayments.map(renderPaymentCard)}
          </View>
        )}

        {/* All Payments */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>All Payments</Text>
          
          {payments.length > 0 ? (
            payments.map(renderPaymentCard)
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="card-outline" size={64} color="#ccc" />
              <Text style={styles.emptyStateTitle}>No payments found</Text>
              <Text style={styles.emptyStateText}>
                Your payment history will appear here once you start making payments
              </Text>
            </View>
          )}
        </View>

        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Methods</Text>
          
          <TouchableOpacity style={styles.methodCard}>
            <View style={styles.methodInfo}>
              <Ionicons name="card" size={24} color="#2E7D32" />
              <View style={styles.methodDetails}>
                <Text style={styles.methodName}>Bank Transfer</Text>
                <Text style={styles.methodDescription}>
                  Transfer directly from your bank account
                </Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.methodCard}>
            <View style={styles.methodInfo}>
              <Ionicons name="phone-portrait" size={24} color="#2E7D32" />
              <View style={styles.methodDetails}>
                <Text style={styles.methodName}>Mobile Money</Text>
                <Text style={styles.methodDescription}>
                  Pay using your mobile wallet
                </Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.methodCard}>
            <View style={styles.methodInfo}>
              <Ionicons name="card-outline" size={24} color="#2E7D32" />
              <View style={styles.methodDetails}>
                <Text style={styles.methodName}>Debit Card</Text>
                <Text style={styles.methodDescription}>
                  Pay with your debit card
                </Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  summaryContainer: {
    backgroundColor: '#fff',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  summaryCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  section: {
    padding: 20,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  paymentCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  paymentInfo: {
    flex: 1,
  },
  paymentAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  paymentDate: {
    fontSize: 14,
    color: '#666',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '500',
    marginLeft: 4,
  },
  paymentDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  payNowButton: {
    backgroundColor: '#2E7D32',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  payNowText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  methodCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  methodDetails: {
    marginLeft: 16,
    flex: 1,
  },
  methodName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  methodDescription: {
    fontSize: 14,
    color: '#666',
  },
});

export default PaymentsScreen;
