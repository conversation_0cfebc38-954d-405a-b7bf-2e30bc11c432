import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { loansAPI, formatCurrency, formatDate } from '../services/api';

const LoansScreen = ({ navigation }) => {
  const [loans, setLoans] = useState([]);
  const [filteredLoans, setFilteredLoans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filters = [
    { key: 'all', label: 'All', count: 0 },
    { key: 'active', label: 'Active', count: 0 },
    { key: 'completed', label: 'Completed', count: 0 },
    { key: 'pending', label: 'Pending', count: 0 },
  ];

  const fetchLoans = useCallback(async () => {
    try {
      const response = await loansAPI.getLoans();
      if (response.success) {
        setLoans(response.data);
        filterLoans(response.data, selectedFilter);
      }
    } catch (error) {
      console.error('Loans fetch error:', error);
      Alert.alert('Error', 'Failed to load loans');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [selectedFilter]);

  useEffect(() => {
    fetchLoans();
  }, [fetchLoans]);

  const filterLoans = (loansList, filter) => {
    if (filter === 'all') {
      setFilteredLoans(loansList);
    } else {
      setFilteredLoans(loansList.filter(loan => loan.status === filter));
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchLoans();
  }, [fetchLoans]);

  const handleFilterChange = (filter) => {
    setSelectedFilter(filter);
    filterLoans(loans, filter);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return '#4CAF50';
      case 'completed':
        return '#2196F3';
      case 'pending':
        return '#FF9800';
      default:
        return '#666';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return 'checkmark-circle';
      case 'completed':
        return 'checkmark-done-circle';
      case 'pending':
        return 'time';
      default:
        return 'help-circle';
    }
  };

  const getFilterCounts = () => {
    const counts = {
      all: loans.length,
      active: loans.filter(l => l.status === 'active').length,
      completed: loans.filter(l => l.status === 'completed').length,
      pending: loans.filter(l => l.status === 'pending').length,
    };
    return counts;
  };

  const renderLoanCard = (loan) => (
    <TouchableOpacity
      key={loan.id}
      style={styles.loanCard}
      onPress={() => navigation.navigate('LoanDetails', { loanId: loan.id })}
    >
      <View style={styles.loanHeader}>
        <View style={styles.loanInfo}>
          <Text style={styles.loanAmount}>{formatCurrency(loan.amount)}</Text>
          <Text style={styles.loanPurpose}>{loan.purpose}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(loan.status) }]}>
          <Ionicons name={getStatusIcon(loan.status)} size={12} color="#fff" />
          <Text style={styles.statusText}>
            {loan.status.charAt(0).toUpperCase() + loan.status.slice(1)}
          </Text>
        </View>
      </View>

      <View style={styles.loanDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Term:</Text>
          <Text style={styles.detailValue}>{loan.termMonths} months</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Rate:</Text>
          <Text style={styles.detailValue}>{loan.interestRate}%</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Monthly Payment:</Text>
          <Text style={styles.detailValue}>{formatCurrency(loan.monthlyPayment)}</Text>
        </View>
      </View>

      {loan.status === 'active' && (
        <View style={styles.progressSection}>
          <View style={styles.progressInfo}>
            <Text style={styles.progressLabel}>Remaining Balance</Text>
            <Text style={styles.progressAmount}>
              {formatCurrency(loan.remainingBalance)}
            </Text>
          </View>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${((loan.amount - loan.remainingBalance) / loan.amount) * 100}%`,
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {loan.paymentsCompleted} of {loan.termMonths} payments completed
          </Text>
        </View>
      )}

      {loan.status === 'pending' && (
        <View style={styles.pendingInfo}>
          <Text style={styles.pendingText}>
            Application submitted on {formatDate(loan.applicationDate)}
          </Text>
        </View>
      )}

      {loan.status === 'completed' && (
        <View style={styles.completedInfo}>
          <Text style={styles.completedText}>
            Loan completed • Total paid: {formatCurrency(loan.totalAmount)}
          </Text>
        </View>
      )}

      <View style={styles.cardFooter}>
        <Text style={styles.dateText}>
          Applied: {formatDate(loan.applicationDate)}
        </Text>
        <Ionicons name="chevron-forward" size={16} color="#666" />
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2E7D32" />
          <Text style={styles.loadingText}>Loading loans...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const filterCounts = getFilterCounts();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.title}>My Loans</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('Calculator')}
        >
          <Ionicons name="add" size={24} color="#2E7D32" />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}
        contentContainerStyle={styles.filterContent}
      >
        {filters.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterTab,
              selectedFilter === filter.key && styles.filterTabActive,
            ]}
            onPress={() => handleFilterChange(filter.key)}
          >
            <Text
              style={[
                styles.filterText,
                selectedFilter === filter.key && styles.filterTextActive,
              ]}
            >
              {filter.label} ({filterCounts[filter.key]})
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Loans List */}
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {filteredLoans.length > 0 ? (
          <View style={styles.loansContainer}>
            {filteredLoans.map(renderLoanCard)}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color="#ccc" />
            <Text style={styles.emptyStateTitle}>No loans found</Text>
            <Text style={styles.emptyStateText}>
              {selectedFilter === 'all'
                ? "You haven't applied for any loans yet"
                : `No ${selectedFilter} loans found`}
            </Text>
            <TouchableOpacity
              style={styles.emptyStateButton}
              onPress={() => navigation.navigate('Calculator')}
            >
              <Text style={styles.emptyStateButtonText}>Calculate a Loan</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    padding: 8,
  },
  filterContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  filterContent: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  filterTabActive: {
    backgroundColor: '#2E7D32',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  filterTextActive: {
    color: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  loansContainer: {
    padding: 20,
  },
  loanCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  loanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  loanInfo: {
    flex: 1,
  },
  loanAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  loanPurpose: {
    fontSize: 16,
    color: '#666',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  loanDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  progressAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#e0e0e0',
    borderRadius: 3,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#2E7D32',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  pendingInfo: {
    backgroundColor: '#FFF3E0',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  pendingText: {
    fontSize: 14,
    color: '#F57C00',
    textAlign: 'center',
  },
  completedInfo: {
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  completedText: {
    fontSize: 14,
    color: '#1976D2',
    textAlign: 'center',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  dateText: {
    fontSize: 12,
    color: '#666',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    marginTop: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: '#2E7D32',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default LoansScreen;
