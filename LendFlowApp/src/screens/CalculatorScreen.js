import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { loansAPI, settingsAPI, formatCurrency } from '../services/api';

const CalculatorScreen = ({ navigation }) => {
  const [amount, setAmount] = useState('10000');
  const [termMonths, setTermMonths] = useState(12);
  const [interestRate, setInterestRate] = useState(12);
  const [calculation, setCalculation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState(null);

  useEffect(() => {
    fetchLoanSettings();
  }, []);

  useEffect(() => {
    if (amount && termMonths && interestRate) {
      calculateLoan();
    }
  }, [amount, termMonths, interestRate]);

  const fetchLoanSettings = async () => {
    try {
      const response = await settingsAPI.getLoanSettings();
      if (response.success) {
        setSettings(response.data);
        setInterestRate(response.data.baseInterestRate);
      }
    } catch (error) {
      console.error('Failed to fetch loan settings:', error);
    }
  };

  const calculateLoan = async () => {
    if (!amount || !termMonths || !interestRate) return;

    const loanAmount = parseFloat(amount.replace(/[^0-9.]/g, ''));
    if (isNaN(loanAmount) || loanAmount <= 0) return;

    setLoading(true);
    try {
      const response = await loansAPI.calculateLoan(loanAmount, termMonths, interestRate);
      if (response.success) {
        setCalculation(response.data);
      }
    } catch (error) {
      console.error('Calculation error:', error);
      Alert.alert('Error', 'Failed to calculate loan');
    } finally {
      setLoading(false);
    }
  };

  const formatAmountInput = (text) => {
    // Remove all non-digits
    const cleaned = text.replace(/[^0-9]/g, '');
    if (cleaned === '') return '';
    
    // Convert to number and format
    const number = parseInt(cleaned);
    return number.toLocaleString('en-ZA');
  };

  const handleAmountChange = (text) => {
    const formatted = formatAmountInput(text);
    setAmount(formatted);
  };

  const getMinAmount = () => settings?.minLoanAmount || 1000;
  const getMaxAmount = () => settings?.maxLoanAmount || 100000;
  const getMinTerm = () => settings?.minTermMonths || 6;
  const getMaxTerm = () => settings?.maxTermMonths || 60;
  const getMinRate = () => settings?.baseInterestRate || 8;
  const getMaxRate = () => settings?.maxInterestRate || 30;

  const isValidAmount = () => {
    const numAmount = parseFloat(amount.replace(/[^0-9.]/g, ''));
    return numAmount >= getMinAmount() && numAmount <= getMaxAmount();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Loan Calculator</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Calculator Form */}
        <View style={styles.form}>
          {/* Loan Amount */}
          <View style={styles.inputSection}>
            <Text style={styles.label}>Loan Amount</Text>
            <View style={styles.amountInputContainer}>
              <Text style={styles.currencySymbol}>R</Text>
              <TextInput
                style={styles.amountInput}
                value={amount}
                onChangeText={handleAmountChange}
                placeholder="0"
                keyboardType="numeric"
                maxLength={10}
              />
            </View>
            <Text style={styles.rangeText}>
              Range: {formatCurrency(getMinAmount())} - {formatCurrency(getMaxAmount())}
            </Text>
            {!isValidAmount() && amount && (
              <Text style={styles.errorText}>
                Amount must be between {formatCurrency(getMinAmount())} and {formatCurrency(getMaxAmount())}
              </Text>
            )}
          </View>

          {/* Loan Term */}
          <View style={styles.inputSection}>
            <Text style={styles.label}>Loan Term</Text>
            <View style={styles.sliderContainer}>
              <Text style={styles.sliderValue}>{termMonths} months</Text>
              <Slider
                style={styles.slider}
                minimumValue={getMinTerm()}
                maximumValue={getMaxTerm()}
                value={termMonths}
                onValueChange={setTermMonths}
                step={1}
                minimumTrackTintColor="#2E7D32"
                maximumTrackTintColor="#ddd"
                thumbStyle={styles.sliderThumb}
              />
              <View style={styles.sliderLabels}>
                <Text style={styles.sliderLabel}>{getMinTerm()} months</Text>
                <Text style={styles.sliderLabel}>{getMaxTerm()} months</Text>
              </View>
            </View>
          </View>

          {/* Interest Rate */}
          <View style={styles.inputSection}>
            <Text style={styles.label}>Interest Rate</Text>
            <View style={styles.sliderContainer}>
              <Text style={styles.sliderValue}>{interestRate.toFixed(1)}% per annum</Text>
              <Slider
                style={styles.slider}
                minimumValue={getMinRate()}
                maximumValue={getMaxRate()}
                value={interestRate}
                onValueChange={setInterestRate}
                step={0.5}
                minimumTrackTintColor="#2E7D32"
                maximumTrackTintColor="#ddd"
                thumbStyle={styles.sliderThumb}
              />
              <View style={styles.sliderLabels}>
                <Text style={styles.sliderLabel}>{getMinRate()}%</Text>
                <Text style={styles.sliderLabel}>{getMaxRate()}%</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Calculation Results */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#2E7D32" />
            <Text style={styles.loadingText}>Calculating...</Text>
          </View>
        ) : calculation && isValidAmount() ? (
          <View style={styles.resultsContainer}>
            <Text style={styles.resultsTitle}>Loan Summary</Text>
            
            {/* Monthly Payment */}
            <View style={styles.resultCard}>
              <View style={styles.resultHeader}>
                <Ionicons name="calendar" size={20} color="#2E7D32" />
                <Text style={styles.resultLabel}>Monthly Payment</Text>
              </View>
              <Text style={styles.resultValue}>
                {formatCurrency(calculation.monthlyPayment)}
              </Text>
            </View>

            {/* Total Interest */}
            <View style={styles.resultCard}>
              <View style={styles.resultHeader}>
                <Ionicons name="trending-up" size={20} color="#FF9800" />
                <Text style={styles.resultLabel}>Total Interest</Text>
              </View>
              <Text style={styles.resultValue}>
                {formatCurrency(calculation.totalInterest)}
              </Text>
            </View>

            {/* Total Amount */}
            <View style={styles.resultCard}>
              <View style={styles.resultHeader}>
                <Ionicons name="calculator" size={20} color="#2196F3" />
                <Text style={styles.resultLabel}>Total Amount</Text>
              </View>
              <Text style={styles.resultValue}>
                {formatCurrency(calculation.totalAmount)}
              </Text>
            </View>

            {/* Breakdown */}
            <View style={styles.breakdownCard}>
              <Text style={styles.breakdownTitle}>Payment Breakdown</Text>
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>Principal Amount:</Text>
                <Text style={styles.breakdownValue}>
                  {formatCurrency(calculation.principal)}
                </Text>
              </View>
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>Interest Rate:</Text>
                <Text style={styles.breakdownValue}>{calculation.interestRate}% p.a.</Text>
              </View>
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>Loan Term:</Text>
                <Text style={styles.breakdownValue}>{calculation.termMonths} months</Text>
              </View>
            </View>

            {/* Apply Button */}
            <TouchableOpacity style={styles.applyButton}>
              <Text style={styles.applyButtonText}>Apply for This Loan</Text>
            </TouchableOpacity>
          </View>
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  form: {
    backgroundColor: '#fff',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputSection: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    backgroundColor: '#f8f9fa',
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRightWidth: 1,
    borderRightColor: '#ddd',
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    paddingHorizontal: 16,
    paddingVertical: 16,
    color: '#333',
  },
  rangeText: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#f44336',
    marginTop: 4,
  },
  sliderContainer: {
    marginTop: 8,
  },
  sliderValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E7D32',
    textAlign: 'center',
    marginBottom: 16,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#2E7D32',
    width: 20,
    height: 20,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  sliderLabel: {
    fontSize: 12,
    color: '#666',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  resultsContainer: {
    margin: 20,
    marginTop: 0,
  },
  resultsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  resultCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultLabel: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  resultValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  breakdownCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  breakdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  breakdownLabel: {
    fontSize: 14,
    color: '#666',
  },
  breakdownValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  applyButton: {
    backgroundColor: '#2E7D32',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  applyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CalculatorScreen;
