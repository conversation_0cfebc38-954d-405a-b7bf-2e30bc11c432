import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration
const API_BASE_URL = 'http://localhost:3000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, clear storage and redirect to login
      await AsyncStorage.removeItem('authToken');
      await AsyncStorage.removeItem('userData');
    }
    return Promise.reject(error);
  }
);

// Currency formatting utility for ZAR
export const formatCurrency = (amount) => {
  if (amount === null || amount === undefined) return 'R 0.00';
  return `R ${parseFloat(amount).toLocaleString('en-ZA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

// Date formatting utility
export const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Auth API
export const authAPI = {
  login: async (phoneNumber, pin) => {
    try {
      const response = await api.post('/auth/login', {
        phoneNumber,
        pin,
      });
      
      if (response.data.success) {
        // Store token and user data
        await AsyncStorage.setItem('authToken', response.data.data.token);
        await AsyncStorage.setItem('userData', JSON.stringify(response.data.data.user));
      }
      
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Login failed' };
    }
  },

  logout: async () => {
    try {
      await AsyncStorage.removeItem('authToken');
      await AsyncStorage.removeItem('userData');
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: 'Logout failed' };
    }
  },

  getCurrentUser: async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  isAuthenticated: async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      return !!token;
    } catch (error) {
      return false;
    }
  },
};

// Loans API
export const loansAPI = {
  getLoans: async (status = null) => {
    try {
      const params = status ? { status } : {};
      const response = await api.get('/loans', { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Failed to fetch loans' };
    }
  },

  getLoanSummary: async () => {
    try {
      const response = await api.get('/loans/summary');
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Failed to fetch loan summary' };
    }
  },

  calculateLoan: async (amount, termMonths, interestRate = 12) => {
    try {
      const response = await api.post('/loans/calculate', {
        amount,
        termMonths,
        interestRate,
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Failed to calculate loan' };
    }
  },
};

// Payments API
export const paymentsAPI = {
  getPayments: async () => {
    try {
      const response = await api.get('/payments');
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Failed to fetch payments' };
    }
  },

  getPaymentSummary: async () => {
    try {
      const response = await api.get('/payments/summary');
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Failed to fetch payment summary' };
    }
  },
};

// Profile API
export const profileAPI = {
  getProfile: async () => {
    try {
      const response = await api.get('/profile');
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Failed to fetch profile' };
    }
  },
};

// Loan Settings API
export const settingsAPI = {
  getLoanSettings: async () => {
    try {
      const response = await api.get('/loan-settings');
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Failed to fetch loan settings' };
    }
  },
};

// Health check
export const healthAPI = {
  check: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL.replace('/api/v1', '')}/health`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { success: false, error: 'Health check failed' };
    }
  },
};

export default api;
