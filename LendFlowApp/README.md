# Lend Flow Mobile App

A comprehensive React Native mobile application for loan management, built with Expo and integrated with the Lend Flow API backend.

## 🚀 Features

### 📱 **Mobile-First Design**
- Native iOS and Android support
- Responsive design optimized for mobile devices
- Intuitive touch-friendly interface
- Smooth animations and transitions

### 🔐 **Authentication**
- Phone number + PIN based login
- Secure token-based authentication
- Auto-login with stored credentials
- Logout functionality

### 💰 **Loan Management**
- **Dashboard**: Overview of all loans and financial summary
- **Loan Calculator**: Real-time loan calculations with ZAR currency
- **Loan Listing**: View all loans with status filtering
- **Loan Details**: Comprehensive loan information and progress tracking

### 💳 **Payment Management**
- Payment history and tracking
- Payment summaries and statistics
- Multiple payment method options
- Payment reminders and due dates

### 👤 **Profile Management**
- User profile with personal information
- Credit score display and tracking
- Banking information management
- Settings and preferences

### 💱 **ZAR Currency Support**
- All amounts displayed in South African Rand (R)
- Proper currency formatting (R 1,234.56)
- Localized number formatting for South Africa

## 🛠️ Technology Stack

- **Framework**: React Native with Expo
- **Navigation**: React Navigation (Stack & Tab navigators)
- **State Management**: React Context API
- **HTTP Client**: Axios
- **Storage**: AsyncStorage
- **UI Components**: React Native Paper, Expo Vector Icons
- **Platform**: iOS & Android

## 📦 Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd LendFlowApp
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on device/simulator**
   - **iOS**: Press `i` in the terminal or scan QR code with Camera app
   - **Android**: Press `a` in the terminal or scan QR code with Expo Go app
   - **Web**: Press `w` in the terminal

## 🔧 Configuration

### API Configuration
Update the API base URL in `src/services/api.js`:
```javascript
const API_BASE_URL = 'http://localhost:3000/api/v1'; // Update for production
```

### Demo Credentials
- **Phone Number**: ************
- **PIN**: 9999

## 📱 App Structure

```
src/
├── components/          # Reusable UI components
├── context/            # React Context providers
│   └── AuthContext.js  # Authentication state management
├── navigation/         # Navigation configuration
│   └── AppNavigator.js # Main navigation setup
├── screens/           # App screens
│   ├── LoginScreen.js      # Authentication screen
│   ├── DashboardScreen.js  # Main dashboard
│   ├── CalculatorScreen.js # Loan calculator
│   ├── LoansScreen.js      # Loans listing
│   ├── PaymentsScreen.js   # Payments management
│   └── ProfileScreen.js    # User profile
└── services/          # API and utility services
    └── api.js         # API client and utilities
```

## 🎨 Design System

### Colors
- **Primary**: #2E7D32 (Green)
- **Secondary**: #4CAF50 (Light Green)
- **Background**: #f8f9fa (Light Gray)
- **Surface**: #ffffff (White)
- **Text**: #333333 (Dark Gray)

### Typography
- **Headers**: Bold, 20-24px
- **Body**: Regular, 14-16px
- **Captions**: Regular, 12px

### Components
- **Cards**: Rounded corners (12-16px), subtle shadows
- **Buttons**: Rounded (8-12px), consistent padding
- **Inputs**: Bordered, rounded corners
- **Status Badges**: Color-coded, rounded pills

## 🔗 API Integration

The app integrates with the Lend Flow API backend:

### Endpoints Used
- `POST /auth/login` - User authentication
- `GET /loans` - Fetch user loans
- `GET /loans/summary` - Dashboard summary
- `POST /loans/calculate` - Loan calculations
- `GET /payments` - Payment history
- `GET /profile` - User profile
- `GET /loan-settings` - Loan configuration

### Authentication Flow
1. User enters phone number and PIN
2. App sends credentials to API
3. API returns JWT token and user data
4. Token stored in AsyncStorage
5. Token included in subsequent API requests

## 📊 Features Overview

### Dashboard
- Total outstanding balance in ZAR
- Active loans count
- Quick action buttons
- Recent loans overview
- Next payment due information

### Loan Calculator
- Interactive amount input with ZAR formatting
- Slider controls for term and interest rate
- Real-time calculation updates
- Detailed payment breakdown
- Apply for loan functionality

### Loans Management
- Filter by status (All, Active, Completed, Pending)
- Progress tracking for active loans
- Loan details with payment schedules
- Status indicators with color coding

### Payments
- Payment history with status tracking
- Payment summary statistics
- Multiple payment method options
- Due date tracking and reminders

### Profile
- User information display
- Credit score visualization
- Banking information
- Settings and preferences
- Logout functionality

## 🚀 Deployment

### Production Build
```bash
# Build for production
expo build:android
expo build:ios

# Or using EAS Build (recommended)
eas build --platform android
eas build --platform ios
```

### Environment Configuration
Create environment-specific configurations:
- Development: Local API (localhost:3000)
- Staging: Staging API URL
- Production: Production API URL

## 🧪 Testing

### Manual Testing
1. Start the API server (`node server.js`)
2. Start the mobile app (`npx expo start`)
3. Test login with demo credentials
4. Navigate through all screens
5. Test loan calculator functionality
6. Verify API integration

### Test Scenarios
- [ ] Login with valid credentials
- [ ] Login with invalid credentials
- [ ] Dashboard data loading
- [ ] Loan calculator calculations
- [ ] Navigation between screens
- [ ] Logout functionality
- [ ] API error handling

## 🔒 Security Features

- JWT token-based authentication
- Secure storage with AsyncStorage
- API request/response interceptors
- Automatic token refresh handling
- Secure PIN input with masking

## 📈 Performance Optimizations

- Lazy loading of screens
- Optimized image handling
- Efficient state management
- Minimal re-renders with React.memo
- Proper cleanup of subscriptions

## 🐛 Troubleshooting

### Common Issues

1. **Metro bundler port conflict**
   - Solution: Use different port (8082) when prompted

2. **API connection issues**
   - Check API server is running on localhost:3000
   - Verify network connectivity
   - Update API base URL for device testing

3. **Package version warnings**
   - Update packages to recommended versions
   - Run `npm install` to resolve dependencies

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the API documentation

---

**Lend Flow Mobile App v1.0.0**  
*Your trusted loan partner, now on mobile* 📱💰
