{"name": "lend-flow", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-community/slider": "^4.5.7", "@react-navigation/bottom-tabs": "^7.3.17", "@react-navigation/native": "^7.1.13", "@react-navigation/stack": "^7.3.6", "@supabase/supabase-js": "^2.50.0", "expo": "~53.0.12", "expo-linear-gradient": "^14.1.5", "expo-local-authentication": "^16.0.4", "expo-notifications": "^0.31.3", "expo-secure-store": "^14.2.3", "expo-sms": "^13.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-paper": "^5.14.5", "react-native-progress": "^5.0.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}