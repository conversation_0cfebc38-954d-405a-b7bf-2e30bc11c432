{"name": "expo-notifications", "version": "0.31.3", "description": "Provides an API to fetch push notification tokens and to present, schedule, receive, and respond to notifications.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": ["./build/DevicePushTokenAutoRegistration.fx.js"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "notifications", "expo-notifications", "push-nofifications"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-notifications"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/notifications/", "jest": {"projects": [{"preset": "jest-expo/ios"}]}, "dependencies": {"@expo/image-utils": "^0.7.4", "@ide/backoff": "^1.0.0", "abort-controller": "^3.0.0", "assert": "^2.0.0", "badgin": "^1.1.5", "expo-application": "~6.1.4", "expo-constants": "~17.1.6"}, "devDependencies": {"expo-module-scripts": "^4.1.7", "memfs": "^3.2.0"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "gitHead": "7638c800b57fe78f57cc7f129022f58e84a523c5"}