{"version": 3, "file": "BadgeModule.web.js", "sourceRoot": "", "sources": ["../src/BadgeModule.web.ts"], "names": [], "mappings": "AAEA,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAE1B,MAAM,WAAW,GAAgB;IAC/B,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;IACrB,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;IACzB,kBAAkB,EAAE,KAAK,IAAI,EAAE;QAC7B,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IACD,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE;QAChD,uEAAuE;QACvE,kFAAkF;QAClF,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;QACD,iBAAiB,GAAG,UAAU,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAC;AAEF,eAAe,WAAW,CAAC", "sourcesContent": ["import { BadgeModule } from './BadgeModule.types';\n\nlet lastSetBadgeCount = 0;\n\nconst badgeModule: BadgeModule = {\n  addListener: () => {},\n  removeListeners: () => {},\n  getBadgeCountAsync: async () => {\n    return lastSetBadgeCount;\n  },\n  setBadgeCountAsync: async (badgeCount, options) => {\n    // If this module is loaded in SSR (NextJS), we can't modify the badge.\n    // It also can't load the badgin module, that instantly invokes methods on window.\n    if (typeof window === 'undefined') {\n      return false;\n    }\n    const badgin = require('badgin');\n    if (badgeCount > 0) {\n      badgin.set(badgeCount, options);\n    } else {\n      badgin.clear();\n    }\n    lastSetBadgeCount = badgeCount;\n    return true;\n  },\n};\n\nexport default badgeModule;\n"]}