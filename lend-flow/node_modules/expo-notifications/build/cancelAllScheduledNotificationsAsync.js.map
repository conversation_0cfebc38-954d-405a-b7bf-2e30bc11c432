{"version": 3, "file": "cancelAllScheduledNotificationsAsync.js", "sourceRoot": "", "sources": ["../src/cancelAllScheduledNotificationsAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,qBAAqB,MAAM,yBAAyB,CAAC;AAE5D;;;;GAIG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,oCAAoC;IAChE,IAAI,CAAC,qBAAqB,CAAC,oCAAoC,EAAE,CAAC;QAChE,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,sCAAsC,CAAC,CAAC;IACzF,CAAC;IAED,OAAO,MAAM,qBAAqB,CAAC,oCAAoC,EAAE,CAAC;AAC5E,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationScheduler from './NotificationScheduler';\n\n/**\n * Cancels all scheduled notifications.\n * @return A Promise that resolves once all the scheduled notifications are successfully canceled, or if there are no scheduled notifications.\n * @header schedule\n */\nexport default async function cancelAllScheduledNotificationsAsync(): Promise<void> {\n  if (!NotificationScheduler.cancelAllScheduledNotificationsAsync) {\n    throw new UnavailabilityError('Notifications', 'cancelAllScheduledNotificationsAsync');\n  }\n\n  return await NotificationScheduler.cancelAllScheduledNotificationsAsync();\n}\n"]}