{"version": 3, "file": "dismissAllNotificationsAsync.js", "sourceRoot": "", "sources": ["../src/dismissAllNotificationsAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,qBAAqB,MAAM,+BAA+B,CAAC;AAElE;;;;GAIG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,4BAA4B;IACxD,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,EAAE,CAAC;QACxD,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,8BAA8B,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,MAAM,qBAAqB,CAAC,4BAA4B,EAAE,CAAC;AACpE,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationPresenter from './NotificationPresenterModule';\n\n/**\n * Removes all application's notifications displayed in the notification tray (Notification Center).\n * @return A Promise which resolves once the request to dismiss the notifications is successfully dispatched to the notifications manager.\n * @header dismiss\n */\nexport default async function dismissAllNotificationsAsync(): Promise<void> {\n  if (!NotificationPresenter.dismissAllNotificationsAsync) {\n    throw new UnavailabilityError('Notifications', 'dismissAllNotificationsAsync');\n  }\n\n  return await NotificationPresenter.dismissAllNotificationsAsync();\n}\n"]}