{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,MAAM,CAAC;AAEzC,SAAS,oBAAoB;IAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,MAAM,CAAC;AACpG,CAAC;AAED,IAAI,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;IACnD,MAAM,OAAO,GACX,yEAAyE;QACzE,iHAAiH,CAAC;IACpH,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACxB,CAAC;AAED,OAAO,EAAE,OAAO,IAAI,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAC/E,OAAO,EAAE,OAAO,IAAI,+BAA+B,EAAE,MAAM,mCAAmC,CAAC;AAC/F,OAAO,EAAE,OAAO,IAAI,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAC3E,OAAO,EAAE,OAAO,IAAI,8BAA8B,EAAE,MAAM,kCAAkC,CAAC;AAC7F,OAAO,EAAE,OAAO,IAAI,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AACjF,OAAO,EAAE,OAAO,IAAI,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AACjF,OAAO,EAAE,OAAO,IAAI,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AACzF,OAAO,EAAE,OAAO,IAAI,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AACzF,OAAO,EAAE,OAAO,IAAI,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AACvF,OAAO,EAAE,OAAO,IAAI,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AACvF,OAAO,EAAE,OAAO,IAAI,8BAA8B,EAAE,MAAM,kCAAkC,CAAC;AAC7F,OAAO,EAAE,OAAO,IAAI,iCAAiC,EAAE,MAAM,qCAAqC,CAAC;AACnG,OAAO,EAAE,OAAO,IAAI,gCAAgC,EAAE,MAAM,oCAAoC,CAAC;AACjG,OAAO,EAAE,OAAO,IAAI,gCAAgC,EAAE,MAAM,oCAAoC,CAAC;AACjG,OAAO,EAAE,OAAO,IAAI,mCAAmC,EAAE,MAAM,uCAAuC,CAAC;AACvG,OAAO,EAAE,OAAO,IAAI,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AACrE,OAAO,EAAE,OAAO,IAAI,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AACrE,OAAO,EAAE,OAAO,IAAI,iCAAiC,EAAE,MAAM,qCAAqC,CAAC;AACnG,OAAO,EAAE,OAAO,IAAI,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACnF,OAAO,EAAE,OAAO,IAAI,gCAAgC,EAAE,MAAM,oCAAoC,CAAC;AACjG,OAAO,EAAE,OAAO,IAAI,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AACzG,OAAO,EAAE,OAAO,IAAI,8BAA8B,EAAE,MAAM,kCAAkC,CAAC;AAC7F,OAAO,EAAE,OAAO,IAAI,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AACzF,OAAO,EAAE,OAAO,IAAI,+BAA+B,EAAE,MAAM,mCAAmC,CAAC;AAC/F,OAAO,EAAE,OAAO,IAAI,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAC/E,OAAO,EAAE,OAAO,IAAI,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AACvF,OAAO,EAAE,qCAAqC,EAAE,MAAM,sCAAsC,CAAC;AAC7F,OAAO,EAAE,OAAO,IAAI,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACnE,OAAO,EAAE,OAAO,IAAI,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AACvE,cAAc,gBAAgB,CAAC;AAC/B,cAAc,wBAAwB,CAAC;AACvC,cAAc,wBAAwB,CAAC;AACvC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,yCAAyC,CAAC;AACxD,cAAc,oCAAoC,CAAC;AACnD,cAAc,iCAAiC,CAAC;AAChD,cAAc,uBAAuB,CAAC;AACtC,cAAc,gBAAgB,CAAC", "sourcesContent": ["import { isRunningInExpoGo } from 'expo';\n\nfunction areWeTestingWithJest() {\n  return process.env.JEST_WORKER_ID !== undefined || process.env.NODE_ENV?.toLowerCase() === 'test';\n}\n\nif (isRunningInExpoGo() && !areWeTestingWithJest()) {\n  const message =\n    '`expo-notifications` functionality is not fully supported in Expo Go:\\n' +\n    'We recommend you instead use a development build to avoid limitations. Learn more: https://expo.fyi/dev-client.';\n  console.warn(message);\n}\n\nexport { default as getDevicePushTokenAsync } from './getDevicePushTokenAsync';\nexport { default as unregisterForNotificationsAsync } from './unregisterForNotificationsAsync';\nexport { default as getExpoPushTokenAsync } from './getExpoPushTokenAsync';\nexport { default as getPresentedNotificationsAsync } from './getPresentedNotificationsAsync';\nexport { default as presentNotificationAsync } from './presentNotificationAsync';\nexport { default as dismissNotificationAsync } from './dismissNotificationAsync';\nexport { default as dismissAllNotificationsAsync } from './dismissAllNotificationsAsync';\nexport { default as getNotificationChannelsAsync } from './getNotificationChannelsAsync';\nexport { default as getNotificationChannelAsync } from './getNotificationChannelAsync';\nexport { default as setNotificationChannelAsync } from './setNotificationChannelAsync';\nexport { default as deleteNotificationChannelAsync } from './deleteNotificationChannelAsync';\nexport { default as getNotificationChannelGroupsAsync } from './getNotificationChannelGroupsAsync';\nexport { default as getNotificationChannelGroupAsync } from './getNotificationChannelGroupAsync';\nexport { default as setNotificationChannelGroupAsync } from './setNotificationChannelGroupAsync';\nexport { default as deleteNotificationChannelGroupAsync } from './deleteNotificationChannelGroupAsync';\nexport { default as getBadgeCountAsync } from './getBadgeCountAsync';\nexport { default as setBadgeCountAsync } from './setBadgeCountAsync';\nexport { default as getAllScheduledNotificationsAsync } from './getAllScheduledNotificationsAsync';\nexport { default as scheduleNotificationAsync } from './scheduleNotificationAsync';\nexport { default as cancelScheduledNotificationAsync } from './cancelScheduledNotificationAsync';\nexport { default as cancelAllScheduledNotificationsAsync } from './cancelAllScheduledNotificationsAsync';\nexport { default as getNotificationCategoriesAsync } from './getNotificationCategoriesAsync';\nexport { default as setNotificationCategoryAsync } from './setNotificationCategoryAsync';\nexport { default as deleteNotificationCategoryAsync } from './deleteNotificationCategoryAsync';\nexport { default as getNextTriggerDateAsync } from './getNextTriggerDateAsync';\nexport { default as useLastNotificationResponse } from './useLastNotificationResponse';\nexport { setAutoServerRegistrationEnabledAsync } from './DevicePushTokenAutoRegistration.fx';\nexport { default as registerTaskAsync } from './registerTaskAsync';\nexport { default as unregisterTaskAsync } from './unregisterTaskAsync';\nexport * from './TokenEmitter';\nexport * from './NotificationsEmitter';\nexport * from './NotificationsHandler';\nexport * from './NotificationPermissions';\nexport * from './NotificationChannelGroupManager.types';\nexport * from './NotificationChannelManager.types';\nexport * from './NotificationPermissions.types';\nexport * from './Notifications.types';\nexport * from './Tokens.types';\n"]}