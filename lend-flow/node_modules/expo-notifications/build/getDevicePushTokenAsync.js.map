{"version": 3, "file": "getDevicePushTokenAsync.js", "sourceRoot": "", "sources": ["../src/getDevicePushTokenAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAElE,OAAO,gBAAgB,MAAM,oBAAoB,CAAC;AAElD,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAEhE,IAAI,kBAAkB,GAA2B,IAAI,CAAC;AAEtD;;;;GAIG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,uBAAuB;IACnD,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;QAC9C,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,yBAAyB,CAAC,CAAC;IAChF,CAAC;IACD,qBAAqB,EAAE,CAAC;IAExB,IAAI,eAA8B,CAAC;IACnC,IAAI,kBAAkB,EAAE,CAAC;QACvB,yBAAyB;QACzB,eAAe,GAAG,MAAM,kBAAkB,CAAC;IAC7C,CAAC;SAAM,CAAC;QACN,+CAA+C;QAC/C,kBAAkB,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;QAChE,eAAe,GAAG,MAAM,kBAAkB,CAAC;QAC3C,kBAAkB,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,uFAAuF;IACvF,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;AACtD,CAAC", "sourcesContent": ["import { UnavailabilityError, Platform } from 'expo-modules-core';\n\nimport PushTokenManager from './PushTokenManager';\nimport { DevicePushToken } from './Tokens.types';\nimport { warnOfExpoGoPushUsage } from './warnOfExpoGoPushUsage';\n\nlet nativeTokenPromise: Promise<string> | null = null;\n\n/**\n * Returns a native FCM, APNs token or a [`PushSubscription` data](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * that can be used with another push notification service.\n * @header fetch\n */\nexport default async function getDevicePushTokenAsync(): Promise<DevicePushToken> {\n  if (!PushTokenManager.getDevicePushTokenAsync) {\n    throw new UnavailabilityError('ExpoNotifications', 'getDevicePushTokenAsync');\n  }\n  warnOfExpoGoPushUsage();\n\n  let devicePushToken: string | null;\n  if (nativeTokenPromise) {\n    // Reuse existing Promise\n    devicePushToken = await nativeTokenPromise;\n  } else {\n    // Create a new Promise and clear it afterwards\n    nativeTokenPromise = PushTokenManager.getDevicePushTokenAsync();\n    devicePushToken = await nativeTokenPromise;\n    nativeTokenPromise = null;\n  }\n\n  // @ts-ignore: TS thinks Platform.OS could be anything and can't decide what type is it\n  return { type: Platform.OS, data: devicePushToken };\n}\n"]}