{"version": 3, "file": "getNotificationChannelGroupAsync.android.js", "sourceRoot": "", "sources": ["../src/getNotificationChannelGroupAsync.android.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,+BAA+B,MAAM,mCAAmC,CAAC;AAGhF,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,gCAAgC,CAC5D,OAAe;IAEf,IAAI,CAAC,+BAA+B,CAAC,gCAAgC,EAAE,CAAC;QACtE,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,kCAAkC,CAAC,CAAC;IACrF,CAAC;IAED,OAAO,MAAM,+BAA+B,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC;AACzF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationChannelGroupManager from './NotificationChannelGroupManager';\nimport { NotificationChannelGroup } from './NotificationChannelGroupManager.types';\n\nexport default async function getNotificationChannelGroupAsync(\n  groupId: string\n): Promise<NotificationChannelGroup | null> {\n  if (!NotificationChannelGroupManager.getNotificationChannelGroupAsync) {\n    throw new UnavailabilityError('Notifications', 'getNotificationChannelGroupAsync');\n  }\n\n  return await NotificationChannelGroupManager.getNotificationChannelGroupAsync(groupId);\n}\n"]}