{"version": 3, "file": "NotificationScheduler.native.js", "sourceRoot": "", "sources": ["../src/NotificationScheduler.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAA8B,2BAA2B,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { NotificationSchedulerModule } from './NotificationScheduler.types';\n\nexport default requireNativeModule<NotificationSchedulerModule>('ExpoNotificationScheduler');\n"]}