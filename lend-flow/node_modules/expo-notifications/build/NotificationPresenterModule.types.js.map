{"version": 3, "file": "NotificationPresenterModule.types.js", "sourceRoot": "", "sources": ["../src/NotificationPresenterModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nimport { Notification } from './Notifications.types';\n\nexport interface NotificationPresenterModule extends ProxyNativeModule {\n  getPresentedNotificationsAsync?: () => Promise<Notification[]>;\n  dismissNotificationAsync?: (identifier: string) => Promise<void>;\n  dismissAllNotificationsAsync?: () => Promise<void>;\n}\n"]}