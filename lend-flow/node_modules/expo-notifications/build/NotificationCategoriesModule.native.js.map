{"version": 3, "file": "NotificationCategoriesModule.native.js", "sourceRoot": "", "sources": ["../src/NotificationCategoriesModule.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAChC,kCAAkC,CACnC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { NotificationCategoriesModule } from './NotificationCategoriesModule.types';\n\nexport default requireNativeModule<NotificationCategoriesModule>(\n  'ExpoNotificationCategoriesModule'\n);\n"]}