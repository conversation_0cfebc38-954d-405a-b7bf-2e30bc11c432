{"version": 3, "file": "BackgroundNotificationTasksModule.native.js", "sourceRoot": "", "sources": ["../src/BackgroundNotificationTasksModule.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAChC,uCAAuC,CACxC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { BackgroundNotificationTasksModule } from './BackgroundNotificationTasksModule.types';\n\nexport default requireNativeModule<BackgroundNotificationTasksModule>(\n  'ExpoBackgroundNotificationTasksModule'\n);\n"]}