{"version": 3, "file": "PushTokenManager.types.js", "sourceRoot": "", "sources": ["../src/PushTokenManager.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAMjD,MAAM,OAAO,sBAAuB,SAAQ,YAA0C;IACpF,uBAAuB,CAAyB;IAChD,+BAA+B,CAAuB;CACvD", "sourcesContent": ["import { NativeModule } from 'expo-modules-core';\n\nexport type PushTokenManagerModuleEvents = {\n  onDevicePushToken: (params: { devicePushToken: string }) => void;\n};\n\nexport class PushTokenManagerModule extends NativeModule<PushTokenManagerModuleEvents> {\n  getDevicePushTokenAsync?: () => Promise<string>;\n  unregisterForNotificationsAsync?: () => Promise<void>;\n}\n"]}