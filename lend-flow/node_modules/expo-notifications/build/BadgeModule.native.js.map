{"version": 3, "file": "BadgeModule.native.js", "sourceRoot": "", "sources": ["../src/BadgeModule.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,MAAM,YAAY,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;AAE5D,eAAe;IACb,GAAG,YAAY;IACf,0CAA0C;IAC1C,4CAA4C;IAC5C,uBAAuB;IACvB,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE;QAChD,OAAO,MAAM,YAAY,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;IAC3D,CAAC;CACa,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { BadgeModule } from './BadgeModule.types';\n\nconst nativeModule = requireNativeModule('ExpoBadgeModule');\n\nexport default {\n  ...nativeModule,\n  // We overwrite setBadgeCountAsync to omit\n  // an obsolete options argument when calling\n  // the native function.\n  setBadgeCountAsync: async (badgeCount, options) => {\n    return await nativeModule.setBadgeCountAsync(badgeCount);\n  },\n} as BadgeModule;\n"]}