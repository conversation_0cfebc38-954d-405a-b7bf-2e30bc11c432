{"version": 3, "file": "getNotificationChannelGroupAsync.js", "sourceRoot": "", "sources": ["../src/getNotificationChannelGroupAsync.ts"], "names": [], "mappings": "AAEA;;;;;;;;GAQG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,gCAAgC,CAC5D,OAAe;IAEf,OAAO,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;IAC7E,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { NotificationChannelGroup } from './NotificationChannelGroupManager.types';\n\n/**\n * Fetches information about a single notification channel group.\n * @param groupId The channel group's identifier.\n * @return A Promise which resolves to the channel group object (of type [`NotificationChannelGroup`](#notificationchannelgroup))\n * or to `null` if there was no channel group found for this identifier. On platforms that do not support notification channels,\n * it will always resolve to `null`.\n * @platform android\n * @header channels\n */\nexport default async function getNotificationChannelGroupAsync(\n  groupId: string\n): Promise<NotificationChannelGroup | null> {\n  console.debug('Notification channels feature is only supported on Android.');\n  return null;\n}\n"]}