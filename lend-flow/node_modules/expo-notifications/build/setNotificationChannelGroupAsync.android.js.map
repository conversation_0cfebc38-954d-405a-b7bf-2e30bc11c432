{"version": 3, "file": "setNotificationChannelGroupAsync.android.js", "sourceRoot": "", "sources": ["../src/setNotificationChannelGroupAsync.android.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,+BAA+B,MAAM,mCAAmC,CAAC;AAMhF,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,gCAAgC,CAC5D,OAAe,EACf,KAAoC;IAEpC,IAAI,CAAC,+BAA+B,CAAC,gCAAgC,EAAE,CAAC;QACtE,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,kCAAkC,CAAC,CAAC;IACrF,CAAC;IAED,OAAO,MAAM,+BAA+B,CAAC,gCAAgC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAChG,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationChannelGroupManager from './NotificationChannelGroupManager';\nimport {\n  NotificationChannelGroup,\n  NotificationChannelGroupInput,\n} from './NotificationChannelGroupManager.types';\n\nexport default async function setNotificationChannelGroupAsync(\n  groupId: string,\n  group: NotificationChannelGroupInput\n): Promise<NotificationChannelGroup | null> {\n  if (!NotificationChannelGroupManager.setNotificationChannelGroupAsync) {\n    throw new UnavailabilityError('Notifications', 'setNotificationChannelGroupAsync');\n  }\n\n  return await NotificationChannelGroupManager.setNotificationChannelGroupAsync(groupId, group);\n}\n"]}