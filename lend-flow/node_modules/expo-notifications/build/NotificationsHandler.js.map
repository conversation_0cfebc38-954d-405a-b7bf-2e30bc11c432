{"version": 3, "file": "NotificationsHandler.js", "sourceRoot": "", "sources": ["../src/NotificationsHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,kBAAkB,EAElB,UAAU,EACV,mBAAmB,GACpB,MAAM,mBAAmB,CAAC;AAG3B,OAAO,0BAA0B,MAAM,8BAA8B,CAAC;AACtE,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAElE;;GAEG;AACH,MAAM,OAAO,wBAAyB,SAAQ,UAAU;IACtD,IAAI,CAA6C;IACjD,YAAY,cAAsB,EAAE,YAA0B;QAC5D,KAAK,CAAC,0BAA0B,EAAE,0CAA0C,cAAc,GAAG,CAAC,CAAC;QAC/F,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC;IACnD,CAAC;CACF;AAgCD,iCAAiC;AACjC,MAAM,mBAAmB,GAAG,IAAI,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;AAE/E,MAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAC3D,MAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAEzE,IAAI,kBAAkB,GAA6B,IAAI,CAAC;AACxD,IAAI,yBAAyB,GAA6B,IAAI,CAAC;AAE/D;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,MAAM,UAAU,sBAAsB,CAAC,OAAmC;IACxE,IAAI,kBAAkB,EAAE,CAAC;QACvB,kBAAkB,CAAC,MAAM,EAAE,CAAC;QAC5B,kBAAkB,GAAG,IAAI,CAAC;IAC5B,CAAC;IACD,IAAI,yBAAyB,EAAE,CAAC;QAC9B,yBAAyB,CAAC,MAAM,EAAE,CAAC;QACnC,yBAAyB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACZ,kBAAkB,GAAG,mBAAmB,CAAC,WAAW,CAClD,2BAA2B,EAC3B,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE;YAC7B,IAAI,CAAC,0BAA0B,CAAC,uBAAuB,EAAE,CAAC;gBACxD,OAAO,CAAC,WAAW,EAAE,CACnB,EAAE,EACF,IAAI,mBAAmB,CAAC,eAAe,EAAE,yBAAyB,CAAC,CACpE,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,kBAAkB,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC;gBACzD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;gBAEtE,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;oBAC7B,OAAO,CAAC,IAAI,CACV,sHAAsH,CACvH,CAAC;gBACJ,CAAC;gBACD,MAAM,0BAA0B,CAAC,uBAAuB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gBACvE,OAAO,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,uHAAuH;gBACvH,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACnC,CAAC;QACH,CAAC,CACF,CAAC;QAEF,yBAAyB,GAAG,mBAAmB,CAAC,WAAW,CACzD,kCAAkC,EAClC,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CACvB,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,IAAI,wBAAwB,CAAC,EAAE,EAAE,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAC7F,CAAC;IACJ,CAAC;AACH,CAAC", "sourcesContent": ["import {\n  LegacyEventEmitter,\n  type EventSubscription,\n  CodedError,\n  UnavailabilityError,\n} from 'expo-modules-core';\n\nimport { Notification, NotificationBehavior } from './Notifications.types';\nimport NotificationsHandlerModule from './NotificationsHandlerModule';\nimport { mapNotification } from './utils/mapNotificationResponse';\n\n/**\n * @hidden\n */\nexport class NotificationTimeoutError extends CodedError {\n  info: { notification: Notification; id: string };\n  constructor(notificationId: string, notification: Notification) {\n    super('ERR_NOTIFICATION_TIMEOUT', `Notification handling timed out for ID ${notificationId}.`);\n    this.info = { id: notificationId, notification };\n  }\n}\n\n// @docsMissing\nexport type NotificationHandlingError = NotificationTimeoutError | Error;\n\nexport interface NotificationHandler {\n  /**\n   * A function accepting an incoming notification returning a `Promise` resolving to a behavior ([`NotificationBehavior`](#notificationbehavior))\n   * applicable to the notification\n   * @param notification An object representing the notification.\n   */\n  handleNotification: (notification: Notification) => Promise<NotificationBehavior>;\n  /**\n   * A function called whenever an incoming notification is handled successfully.\n   * @param notificationId Identifier of the notification.\n   */\n  handleSuccess?: (notificationId: string) => void;\n  /**\n   * A function called whenever calling `handleNotification()` for an incoming notification fails.\n   * @param notificationId Identifier of the notification.\n   * @param error An error which occurred in form of `NotificationHandlingError` object.\n   */\n  handleError?: (notificationId: string, error: NotificationHandlingError) => void;\n}\n\ntype HandleNotificationEvent = {\n  id: string;\n  notification: Notification;\n};\n\ntype HandleNotificationTimeoutEvent = HandleNotificationEvent;\n\n// Web uses SyntheticEventEmitter\nconst notificationEmitter = new LegacyEventEmitter(NotificationsHandlerModule);\n\nconst handleNotificationEventName = 'onHandleNotification';\nconst handleNotificationTimeoutEventName = 'onHandleNotificationTimeout';\n\nlet handleSubscription: EventSubscription | null = null;\nlet handleTimeoutSubscription: EventSubscription | null = null;\n\n/**\n * When a notification is received while the app is running, using this function you can set a callback that will decide\n * whether the notification should be shown to the user or not.\n *\n * When a notification is received, `handleNotification` is called with the incoming notification as an argument.\n * The function should respond with a behavior object within 3 seconds, otherwise, the notification will be discarded.\n * If the notification is handled successfully, `handleSuccess` is called with the identifier of the notification,\n * otherwise (or on timeout) `handleError` will be called.\n *\n * The default behavior when the handler is not set or does not respond in time is not to show the notification.\n * @param handler A single parameter which should be either `null` (if you want to clear the handler) or a [`NotificationHandler`](#notificationhandler) object.\n *\n * @example Implementing a notification handler that always shows the notification when it is received.\n * ```jsx\n * import * as Notifications from 'expo-notifications';\n *\n * Notifications.setNotificationHandler({\n *   handleNotification: async () => ({\n *     shouldShowBanner: true,\n *     shouldShowList: true,\n *     shouldPlaySound: false,\n *     shouldSetBadge: false,\n *   }),\n * });\n * ```\n * @header inForeground\n */\nexport function setNotificationHandler(handler: NotificationHandler | null): void {\n  if (handleSubscription) {\n    handleSubscription.remove();\n    handleSubscription = null;\n  }\n  if (handleTimeoutSubscription) {\n    handleTimeoutSubscription.remove();\n    handleTimeoutSubscription = null;\n  }\n\n  if (handler) {\n    handleSubscription = notificationEmitter.addListener<HandleNotificationEvent>(\n      handleNotificationEventName,\n      async ({ id, notification }) => {\n        if (!NotificationsHandlerModule.handleNotificationAsync) {\n          handler.handleError?.(\n            id,\n            new UnavailabilityError('Notifications', 'handleNotificationAsync')\n          );\n          return;\n        }\n\n        try {\n          const mappedNotification = mapNotification(notification);\n          const behavior = await handler.handleNotification(mappedNotification);\n\n          if (behavior.shouldShowAlert) {\n            console.warn(\n              '[expo-notifications]: `shouldShowAlert` is deprecated. Specify `shouldShowBanner` and / or `shouldShowList` instead.'\n            );\n          }\n          await NotificationsHandlerModule.handleNotificationAsync(id, behavior);\n          handler.handleSuccess?.(id);\n        } catch (error: any) {\n          // TODO(@kitten): This callback expects specific Error types, but we never narrow the type before calling this callback\n          handler.handleError?.(id, error);\n        }\n      }\n    );\n\n    handleTimeoutSubscription = notificationEmitter.addListener<HandleNotificationTimeoutEvent>(\n      handleNotificationTimeoutEventName,\n      ({ id, notification }) =>\n        handler.handleError?.(id, new NotificationTimeoutError(id, mapNotification(notification)))\n    );\n  }\n}\n"]}