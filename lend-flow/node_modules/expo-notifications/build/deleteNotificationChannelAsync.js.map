{"version": 3, "file": "deleteNotificationChannelAsync.js", "sourceRoot": "", "sources": ["../src/deleteNotificationChannelAsync.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,8BAA8B,CAAC,SAAiB;IAC5E,OAAO,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;AAC/E,CAAC", "sourcesContent": ["/**\n * Removes the notification channel.\n * @param channelId The channel identifier.\n * @return A Promise which resolving once the channel is removed (or if there was no channel for given identifier).\n * @platform android\n * @header channels\n */\nexport default async function deleteNotificationChannelAsync(channelId: string): Promise<void> {\n  console.debug('Notification channels feature is only supported on Android.');\n}\n"]}