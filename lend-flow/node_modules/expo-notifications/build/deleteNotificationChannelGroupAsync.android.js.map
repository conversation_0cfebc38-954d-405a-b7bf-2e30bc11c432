{"version": 3, "file": "deleteNotificationChannelGroupAsync.android.js", "sourceRoot": "", "sources": ["../src/deleteNotificationChannelGroupAsync.android.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,+BAA+B,MAAM,mCAAmC,CAAC;AAEhF,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,8BAA8B,CAAC,OAAe;IAC1E,IAAI,CAAC,+BAA+B,CAAC,mCAAmC,EAAE,CAAC;QACzE,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,qCAAqC,CAAC,CAAC;IACxF,CAAC;IAED,OAAO,MAAM,+BAA+B,CAAC,mCAAmC,CAAC,OAAO,CAAC,CAAC;AAC5F,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationChannelGroupManager from './NotificationChannelGroupManager';\n\nexport default async function deleteNotificationChannelAsync(groupId: string): Promise<void> {\n  if (!NotificationChannelGroupManager.deleteNotificationChannelGroupAsync) {\n    throw new UnavailabilityError('Notifications', 'deleteNotificationChannelGroupAsync');\n  }\n\n  return await NotificationChannelGroupManager.deleteNotificationChannelGroupAsync(groupId);\n}\n"]}