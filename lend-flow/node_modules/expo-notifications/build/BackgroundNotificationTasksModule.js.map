{"version": 3, "file": "BackgroundNotificationTasksModule.js", "sourceRoot": "", "sources": ["../src/BackgroundNotificationTasksModule.ts"], "names": [], "mappings": "AAAA,eAAe;IACb,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAC", "sourcesContent": ["export default {\n  async registerTaskAsync(taskName: string): Promise<null> {\n    return null;\n  },\n  async unregisterTaskAsync(taskName: string): Promise<null> {\n    return null;\n  },\n};\n"]}