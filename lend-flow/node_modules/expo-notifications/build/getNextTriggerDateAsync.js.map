{"version": 3, "file": "getNextTriggerDateAsync.js", "sourceRoot": "", "sources": ["../src/getNextTriggerDateAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,qBAAqB,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAE3D;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,uBAAuB,CACnD,OAA4C;IAE5C,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;QACnD,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,yBAAyB,CAAC,CAAC;IAChF,CAAC;IAED,OAAO,MAAM,qBAAqB,CAAC,uBAAuB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;AACpF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationScheduler from './NotificationScheduler';\nimport { SchedulableNotificationTriggerInput } from './Notifications.types';\nimport { parseTrigger } from './scheduleNotificationAsync';\n\n/**\n * Allows you to check what will be the next trigger date for given notification trigger input.\n * @param trigger The schedulable notification trigger you would like to check next trigger date for (of type [`SchedulableNotificationTriggerInput`](#schedulablenotificationtriggerinput)).\n * @return If the return value is `null`, the notification won't be triggered. Otherwise, the return value is the Unix timestamp in milliseconds\n * at which the notification will be triggered.\n * @example Calculate next trigger date for a notification trigger:\n * ```ts\n * import * as Notifications from 'expo-notifications';\n *\n * async function logNextTriggerDate() {\n *   try {\n *     const nextTriggerDate = await Notifications.getNextTriggerDateAsync({\n *       hour: 9,\n *       minute: 0,\n *     });\n *     console.log(nextTriggerDate === null ? 'No next trigger date' : new Date(nextTriggerDate));\n *   } catch (e) {\n *     console.warn(`Couldn't have calculated next trigger date: ${e}`);\n *   }\n * }\n * ```\n * @header schedule\n */\nexport default async function getNextTriggerDateAsync(\n  trigger: SchedulableNotificationTriggerInput\n): Promise<number | null> {\n  if (!NotificationScheduler.getNextTriggerDateAsync) {\n    throw new UnavailabilityError('ExpoNotifications', 'getNextTriggerDateAsync');\n  }\n\n  return await NotificationScheduler.getNextTriggerDateAsync(parseTrigger(trigger));\n}\n"]}