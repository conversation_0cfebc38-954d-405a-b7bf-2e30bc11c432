{"version": 3, "file": "setNotificationCategoryAsync.js", "sourceRoot": "", "sources": ["../src/setNotificationCategoryAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,4BAA4B,MAAM,gCAAgC,CAAC;AAO1E;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,4BAA4B,CACxD,UAAkB,EAClB,OAA6B,EAC7B,OAAqC;IAErC,IAAI,CAAC,4BAA4B,CAAC,4BAA4B,EAAE,CAAC;QAC/D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,8BAA8B,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,MAAM,4BAA4B,CAAC,4BAA4B,CACpE,UAAU,EACV,OAAO,EACP,OAAO,CACR,CAAC;AACJ,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationCategoriesModule from './NotificationCategoriesModule';\nimport {\n  NotificationCategory,\n  NotificationAction,\n  NotificationCategoryOptions,\n} from './Notifications.types';\n\n/**\n * Sets the new notification category.\n * @param identifier A string to associate as the ID of this category. You will pass this string in as the `categoryIdentifier`\n * in your [`NotificationContent`](#notificationcontent) to associate a notification with this category.\n * > Don't use the characters `:` or `-` in your category identifier. If you do, categories might not work as expected.\n * @param actions An array of [`NotificationAction`](#notificationaction), which describe the actions associated with this category.\n * @param options An optional object of additional configuration options for your category.\n * @return A Promise which resolves to the category you just have created.\n * @platform android\n * @platform ios\n * @header categories\n */\nexport default async function setNotificationCategoryAsync(\n  identifier: string,\n  actions: NotificationAction[],\n  options?: NotificationCategoryOptions\n): Promise<NotificationCategory> {\n  if (!NotificationCategoriesModule.setNotificationCategoryAsync) {\n    throw new UnavailabilityError('Notifications', 'setNotificationCategoryAsync');\n  }\n\n  return await NotificationCategoriesModule.setNotificationCategoryAsync(\n    identifier,\n    actions,\n    options\n  );\n}\n"]}