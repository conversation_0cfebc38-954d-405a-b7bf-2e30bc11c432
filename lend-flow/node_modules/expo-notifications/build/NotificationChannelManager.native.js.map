{"version": 3, "file": "NotificationChannelManager.native.js", "sourceRoot": "", "sources": ["../src/NotificationChannelManager.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAA6B,gCAAgC,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { NotificationChannelManager } from './NotificationChannelManager.types';\n\nexport default requireNativeModule<NotificationChannelManager>('ExpoNotificationChannelManager');\n"]}