{"version": 3, "file": "PushTokenManager.js", "sourceRoot": "", "sources": ["../src/PushTokenManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC,eAAe;IACb,WAAW,EAAE,GAAG,EAAE;QAChB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CACV,sFAAsF,QAAQ,CAAC,EAAE,0CAA0C,CAC5I,CAAC;YACF,mBAAmB,GAAG,IAAI,CAAC;QAC7B,CAAC;QACD,OAAO;YACL,MAAM,EAAE,GAAG,EAAE,GAAE,CAAC;SACjB,CAAC;IACJ,CAAC;IACD,cAAc,EAAE,GAAG,EAAE,GAAE,CAAC;IACxB,kBAAkB,EAAE,GAAG,EAAE,GAAE,CAAC;IAC5B,IAAI,EAAE,GAAG,EAAE,GAAE,CAAC;IACd,aAAa,EAAE,GAAG,EAAE,CAAC,CAAC;CACG,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nimport { PushTokenManagerModule } from './PushTokenManager.types';\n\nlet warningHasBeenShown = false;\n\nexport default {\n  addListener: () => {\n    if (!warningHasBeenShown) {\n      console.warn(\n        `[expo-notifications] Listening to push token changes is not yet fully supported on ${Platform.OS}. Adding a listener will have no effect.`\n      );\n      warningHasBeenShown = true;\n    }\n    return {\n      remove: () => {},\n    };\n  },\n  removeListener: () => {},\n  removeAllListeners: () => {},\n  emit: () => {},\n  listenerCount: () => 0,\n} as PushTokenManagerModule;\n"]}