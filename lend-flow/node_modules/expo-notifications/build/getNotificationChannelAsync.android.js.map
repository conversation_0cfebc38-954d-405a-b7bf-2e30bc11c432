{"version": 3, "file": "getNotificationChannelAsync.android.js", "sourceRoot": "", "sources": ["../src/getNotificationChannelAsync.android.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,0BAA0B,MAAM,8BAA8B,CAAC;AAGtE,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,2BAA2B,CACvD,SAAiB;IAEjB,IAAI,CAAC,0BAA0B,CAAC,2BAA2B,EAAE,CAAC;QAC5D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,6BAA6B,CAAC,CAAC;IAChF,CAAC;IACD,OAAO,MAAM,0BAA0B,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC;AACjF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationChannelManager from './NotificationChannelManager';\nimport { NotificationChannel } from './NotificationChannelManager.types';\n\nexport default async function getNotificationChannelAsync(\n  channelId: string\n): Promise<NotificationChannel | null> {\n  if (!NotificationChannelManager.getNotificationChannelAsync) {\n    throw new UnavailabilityError('Notifications', 'getNotificationChannelAsync');\n  }\n  return await NotificationChannelManager.getNotificationChannelAsync(channelId);\n}\n"]}