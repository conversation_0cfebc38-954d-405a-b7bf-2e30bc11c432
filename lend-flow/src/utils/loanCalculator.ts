import { LoanCalculation, PaymentScheduleItem } from '../types';

/**
 * Calculate monthly payment using the standard loan payment formula
 * M = P * [r(1+r)^n] / [(1+r)^n - 1]
 * Where:
 * M = Monthly payment
 * P = Principal loan amount
 * r = Monthly interest rate (annual rate / 12)
 * n = Total number of payments (months)
 */
export const calculateMonthlyPayment = (
  principal: number,
  annualInterestRate: number,
  termMonths: number
): number => {
  if (annualInterestRate === 0) {
    return principal / termMonths;
  }
  
  const monthlyRate = annualInterestRate / 100 / 12;
  const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
  const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
  
  return principal * (numerator / denominator);
};

/**
 * Calculate total interest over the life of the loan
 */
export const calculateTotalInterest = (
  monthlyPayment: number,
  termMonths: number,
  principal: number
): number => {
  return (monthlyPayment * termMonths) - principal;
};

/**
 * Generate a complete payment schedule for the loan
 */
export const generatePaymentSchedule = (
  principal: number,
  annualInterestRate: number,
  termMonths: number,
  startDate: Date = new Date()
): PaymentScheduleItem[] => {
  const monthlyPayment = calculateMonthlyPayment(principal, annualInterestRate, termMonths);
  const monthlyRate = annualInterestRate / 100 / 12;
  const schedule: PaymentScheduleItem[] = [];
  
  let remainingBalance = principal;
  
  for (let i = 1; i <= termMonths; i++) {
    const interestAmount = remainingBalance * monthlyRate;
    const principalAmount = monthlyPayment - interestAmount;
    remainingBalance = Math.max(0, remainingBalance - principalAmount);
    
    const paymentDate = new Date(startDate);
    paymentDate.setMonth(paymentDate.getMonth() + i);
    
    schedule.push({
      paymentNumber: i,
      paymentDate: paymentDate.toISOString().split('T')[0],
      paymentAmount: Math.round(monthlyPayment * 100) / 100,
      principalAmount: Math.round(principalAmount * 100) / 100,
      interestAmount: Math.round(interestAmount * 100) / 100,
      remainingBalance: Math.round(remainingBalance * 100) / 100,
    });
  }
  
  return schedule;
};

/**
 * Calculate complete loan details including payment schedule
 */
export const calculateLoan = (
  principal: number,
  annualInterestRate: number,
  termMonths: number,
  startDate?: Date
): LoanCalculation => {
  const monthlyPayment = calculateMonthlyPayment(principal, annualInterestRate, termMonths);
  const totalInterest = calculateTotalInterest(monthlyPayment, termMonths, principal);
  const totalAmount = principal + totalInterest;
  const paymentSchedule = generatePaymentSchedule(principal, annualInterestRate, termMonths, startDate);
  
  return {
    principal: Math.round(principal * 100) / 100,
    interestRate: annualInterestRate,
    termMonths,
    monthlyPayment: Math.round(monthlyPayment * 100) / 100,
    totalInterest: Math.round(totalInterest * 100) / 100,
    totalAmount: Math.round(totalAmount * 100) / 100,
    paymentSchedule,
  };
};

/**
 * Calculate loan affordability based on income and debt-to-income ratio
 */
export const calculateAffordability = (
  monthlyIncome: number,
  existingMonthlyDebts: number,
  maxDebtToIncomeRatio: number = 0.36 // 36% is a common DTI limit
): number => {
  const maxTotalDebt = monthlyIncome * maxDebtToIncomeRatio;
  const availableForNewLoan = maxTotalDebt - existingMonthlyDebts;
  return Math.max(0, availableForNewLoan);
};

/**
 * Calculate maximum loan amount based on affordability
 */
export const calculateMaxLoanAmount = (
  monthlyIncome: number,
  existingMonthlyDebts: number,
  annualInterestRate: number,
  termMonths: number,
  maxDebtToIncomeRatio: number = 0.36
): number => {
  const maxMonthlyPayment = calculateAffordability(
    monthlyIncome,
    existingMonthlyDebts,
    maxDebtToIncomeRatio
  );
  
  if (maxMonthlyPayment <= 0 || annualInterestRate === 0) {
    return 0;
  }
  
  const monthlyRate = annualInterestRate / 100 / 12;
  const denominator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
  const numerator = Math.pow(1 + monthlyRate, termMonths) - 1;
  
  return maxMonthlyPayment * (numerator / denominator);
};

/**
 * Calculate early payoff scenarios
 */
export const calculateEarlyPayoff = (
  currentBalance: number,
  monthlyPayment: number,
  annualInterestRate: number,
  extraPayment: number
): {
  monthsSaved: number;
  interestSaved: number;
  newPayoffDate: string;
} => {
  const monthlyRate = annualInterestRate / 100 / 12;
  const newMonthlyPayment = monthlyPayment + extraPayment;
  
  // Calculate months to payoff with extra payment
  let balance = currentBalance;
  let months = 0;
  let totalInterestPaid = 0;
  
  while (balance > 0 && months < 1000) { // Safety limit
    const interestPayment = balance * monthlyRate;
    const principalPayment = Math.min(newMonthlyPayment - interestPayment, balance);
    
    balance -= principalPayment;
    totalInterestPaid += interestPayment;
    months++;
  }
  
  // Calculate original payoff time
  let originalBalance = currentBalance;
  let originalMonths = 0;
  let originalInterest = 0;
  
  while (originalBalance > 0 && originalMonths < 1000) {
    const interestPayment = originalBalance * monthlyRate;
    const principalPayment = Math.min(monthlyPayment - interestPayment, originalBalance);
    
    originalBalance -= principalPayment;
    originalInterest += interestPayment;
    originalMonths++;
  }
  
  const monthsSaved = originalMonths - months;
  const interestSaved = originalInterest - totalInterestPaid;
  
  const newPayoffDate = new Date();
  newPayoffDate.setMonth(newPayoffDate.getMonth() + months);
  
  return {
    monthsSaved: Math.max(0, monthsSaved),
    interestSaved: Math.max(0, interestSaved),
    newPayoffDate: newPayoffDate.toISOString().split('T')[0],
  };
};

/**
 * Format currency for display (ZAR - South African Rand)
 */
export const formatCurrency = (amount: number | null | undefined, currency: string = 'ZAR'): string => {
  if (amount === null || amount === undefined) return 'R 0.00';

  // For ZAR, use custom formatting to match South African standards
  if (currency === 'ZAR') {
    return `R ${parseFloat(amount.toString()).toLocaleString('en-ZA', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  }

  // Fallback to standard Intl formatting for other currencies
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format percentage for display
 */
export const formatPercentage = (rate: number, decimals: number = 2): string => {
  return `${rate.toFixed(decimals)}%`;
};
