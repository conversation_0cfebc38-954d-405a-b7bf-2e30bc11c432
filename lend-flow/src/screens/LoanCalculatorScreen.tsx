import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { TextInput } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import Slider from '@react-native-community/slider';
import { calculateLoan, formatCurrency, formatPercentage } from '../utils/loanCalculator';
import { LoanCalculation } from '../types';

const LoanCalculatorScreen = () => {
  const navigation = useNavigation();
  const [loanAmount, setLoanAmount] = useState('10000');
  const [interestRate, setInterestRate] = useState(12);
  const [termMonths, setTermMonths] = useState(12);
  const [calculation, setCalculation] = useState<LoanCalculation | null>(null);

  useEffect(() => {
    calculateLoanDetails();
  }, [loanAmount, interestRate, termMonths]);

  const calculateLoanDetails = () => {
    const amount = parseFloat(loanAmount) || 0;
    if (amount > 0) {
      const result = calculateLoan(amount, interestRate, termMonths);
      setCalculation(result);
    } else {
      setCalculation(null);
    }
  };

  const handleAmountChange = (text: string) => {
    // Remove non-numeric characters except decimal point
    const cleaned = text.replace(/[^0-9.]/g, '');
    setLoanAmount(cleaned);
  };

  const formatAmountDisplay = (amount: string) => {
    const num = parseFloat(amount);
    if (isNaN(num)) return '';
    return new Intl.NumberFormat('en-US').format(num);
  };

  const getTermYears = () => {
    return Math.floor(termMonths / 12);
  };

  const getTermMonthsRemainder = () => {
    return termMonths % 12;
  };

  const formatTermDisplay = () => {
    const years = getTermYears();
    const months = getTermMonthsRemainder();
    
    if (years === 0) {
      return `${months} month${months !== 1 ? 's' : ''}`;
    } else if (months === 0) {
      return `${years} year${years !== 1 ? 's' : ''}`;
    } else {
      return `${years} year${years !== 1 ? 's' : ''} ${months} month${months !== 1 ? 's' : ''}`;
    }
  };

  const handleApplyForLoan = () => {
    if (!calculation) {
      Alert.alert('Error', 'Please enter valid loan details');
      return;
    }

    Alert.alert(
      'Apply for Loan',
      `Would you like to apply for a loan of ${formatCurrency(calculation.principal)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Apply', 
          onPress: () => navigation.navigate('LoanApplication', { calculatedLoan: calculation })
        }
      ]
    );
  };

  const ResultCard = ({ title, value, subtitle, icon, color = '#007AFF' }) => (
    <View style={styles.resultCard}>
      <View style={[styles.resultIcon, { backgroundColor: color }]}>
        <Ionicons name={icon} size={20} color="#fff" />
      </View>
      <View style={styles.resultContent}>
        <Text style={styles.resultTitle}>{title}</Text>
        <Text style={styles.resultValue}>{value}</Text>
        {subtitle && <Text style={styles.resultSubtitle}>{subtitle}</Text>}
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <LinearGradient colors={['#007AFF', '#0051D5']} style={styles.header}>
        <Text style={styles.headerTitle}>Loan Calculator</Text>
        <Text style={styles.headerSubtitle}>Calculate your loan payments</Text>
      </LinearGradient>

      {/* Input Section */}
      <View style={styles.inputSection}>
        <Text style={styles.sectionTitle}>Loan Details</Text>
        
        {/* Loan Amount */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Loan Amount</Text>
          <TextInput
            mode="outlined"
            value={formatAmountDisplay(loanAmount)}
            onChangeText={handleAmountChange}
            keyboardType="numeric"
            style={styles.textInput}
            left={<TextInput.Icon icon="currency-usd" />}
            placeholder="Enter loan amount"
            theme={{
              colors: {
                primary: '#007AFF',
                outline: '#ddd',
              },
            }}
          />
        </View>

        {/* Interest Rate */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>
            Interest Rate: {formatPercentage(interestRate)}
          </Text>
          <View style={styles.sliderContainer}>
            <Text style={styles.sliderLabel}>5%</Text>
            <Slider
              style={styles.slider}
              minimumValue={5}
              maximumValue={30}
              value={interestRate}
              onValueChange={setInterestRate}
              step={0.5}
              minimumTrackTintColor="#007AFF"
              maximumTrackTintColor="#ddd"
              thumbStyle={styles.sliderThumb}
            />
            <Text style={styles.sliderLabel}>30%</Text>
          </View>
        </View>

        {/* Loan Term */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>
            Loan Term: {formatTermDisplay()}
          </Text>
          <View style={styles.sliderContainer}>
            <Text style={styles.sliderLabel}>6m</Text>
            <Slider
              style={styles.slider}
              minimumValue={6}
              maximumValue={60}
              value={termMonths}
              onValueChange={setTermMonths}
              step={1}
              minimumTrackTintColor="#007AFF"
              maximumTrackTintColor="#ddd"
              thumbStyle={styles.sliderThumb}
            />
            <Text style={styles.sliderLabel}>5y</Text>
          </View>
        </View>
      </View>

      {/* Results Section */}
      {calculation && (
        <View style={styles.resultsSection}>
          <Text style={styles.sectionTitle}>Calculation Results</Text>
          
          <ResultCard
            title="Monthly Payment"
            value={formatCurrency(calculation.monthlyPayment)}
            icon="calendar"
            color="#28a745"
          />
          
          <ResultCard
            title="Total Interest"
            value={formatCurrency(calculation.totalInterest)}
            subtitle={`${((calculation.totalInterest / calculation.principal) * 100).toFixed(1)}% of principal`}
            icon="trending-up"
            color="#ff6b35"
          />
          
          <ResultCard
            title="Total Amount"
            value={formatCurrency(calculation.totalAmount)}
            subtitle="Principal + Interest"
            icon="calculator"
            color="#6f42c1"
          />

          {/* Payment Breakdown */}
          <View style={styles.breakdownCard}>
            <Text style={styles.breakdownTitle}>Payment Breakdown</Text>
            <View style={styles.breakdownRow}>
              <Text style={styles.breakdownLabel}>Principal Amount:</Text>
              <Text style={styles.breakdownValue}>{formatCurrency(calculation.principal)}</Text>
            </View>
            <View style={styles.breakdownRow}>
              <Text style={styles.breakdownLabel}>Total Interest:</Text>
              <Text style={styles.breakdownValue}>{formatCurrency(calculation.totalInterest)}</Text>
            </View>
            <View style={[styles.breakdownRow, styles.breakdownTotal]}>
              <Text style={styles.breakdownLabelBold}>Total Payable:</Text>
              <Text style={styles.breakdownValueBold}>{formatCurrency(calculation.totalAmount)}</Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={styles.applyButton}
              onPress={handleApplyForLoan}
            >
              <Ionicons name="document-text" size={20} color="#fff" style={styles.buttonIcon} />
              <Text style={styles.applyButtonText}>Apply for This Loan</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.scheduleButton}
              onPress={() => {
                // Navigate to payment schedule view
                Alert.alert('Payment Schedule', 'Payment schedule feature coming soon!');
              }}
            >
              <Ionicons name="list" size={20} color="#007AFF" style={styles.buttonIcon} />
              <Text style={styles.scheduleButtonText}>View Payment Schedule</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Tips Section */}
      <View style={styles.tipsSection}>
        <Text style={styles.sectionTitle}>💡 Tips</Text>
        <View style={styles.tipCard}>
          <Text style={styles.tipText}>
            • Lower interest rates mean lower monthly payments
          </Text>
          <Text style={styles.tipText}>
            • Shorter loan terms save money on interest
          </Text>
          <Text style={styles.tipText}>
            • Consider your monthly budget when choosing loan terms
          </Text>
          <Text style={styles.tipText}>
            • Compare different scenarios to find the best option
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  inputSection: {
    padding: 20,
    marginTop: -20,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#fff',
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  slider: {
    flex: 1,
    height: 40,
    marginHorizontal: 16,
  },
  sliderThumb: {
    backgroundColor: '#007AFF',
    width: 20,
    height: 20,
  },
  sliderLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  resultsSection: {
    padding: 20,
  },
  resultCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  resultValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  resultSubtitle: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  breakdownCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  breakdownTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  breakdownTotal: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    marginTop: 8,
    paddingTop: 12,
  },
  breakdownLabel: {
    fontSize: 14,
    color: '#666',
  },
  breakdownValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  breakdownLabelBold: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
  breakdownValueBold: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: 'bold',
  },
  actionButtons: {
    marginBottom: 20,
  },
  applyButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  applyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  scheduleButton: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  scheduleButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonIcon: {
    marginRight: 8,
  },
  tipsSection: {
    padding: 20,
    paddingTop: 0,
  },
  tipCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
});

export default LoanCalculatorScreen;
