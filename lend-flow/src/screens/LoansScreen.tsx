import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { loanService } from '../services/supabase';
import { Loan } from '../types';
import { formatCurrency } from '../utils/loanCalculator';

const LoansScreen = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const [loans, setLoans] = useState<Loan[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');

  useEffect(() => {
    loadLoans();
  }, [user]);

  const loadLoans = async () => {
    if (!user) return;
    
    try {
      const userLoans = await loanService.getUserLoans(user.id);
      setLoans(userLoans);
    } catch (error) {
      console.error('Error loading loans:', error);
    }
  };

  const onRefresh = async () => {
    setIsRefreshing(true);
    await loadLoans();
    setIsRefreshing(false);
  };

  const getFilteredLoans = () => {
    if (selectedFilter === 'all') return loans;
    return loans.filter(loan => loan.status === selectedFilter);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'rejected': return '#dc3545';
      default: return '#007AFF';
    }
  };

  const LoanCard = ({ loan }: { loan: Loan }) => (
    <TouchableOpacity 
      style={styles.loanCard}
      onPress={() => navigation.navigate('LoanDetails', { loanId: loan.id })}
    >
      <View style={styles.loanHeader}>
        <View>
          <Text style={styles.loanTitle}>Loan #{loan.id.slice(-6)}</Text>
          <Text style={styles.loanDate}>Applied: {new Date(loan.applicationDate).toLocaleDateString()}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(loan.status) }]}>
          <Text style={styles.statusText}>{loan.status.toUpperCase()}</Text>
        </View>
      </View>
      
      <View style={styles.loanDetails}>
        <View style={styles.loanDetailRow}>
          <Text style={styles.loanDetailLabel}>Original Amount:</Text>
          <Text style={styles.loanDetailValue}>{formatCurrency(loan.amount)}</Text>
        </View>
        <View style={styles.loanDetailRow}>
          <Text style={styles.loanDetailLabel}>Remaining Balance:</Text>
          <Text style={styles.loanDetailValue}>{formatCurrency(loan.remainingBalance)}</Text>
        </View>
        <View style={styles.loanDetailRow}>
          <Text style={styles.loanDetailLabel}>Monthly Payment:</Text>
          <Text style={styles.loanDetailValue}>{formatCurrency(loan.monthlyPayment)}</Text>
        </View>
      </View>

      {loan.status === 'active' && (
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${((loan.amount - loan.remainingBalance) / loan.amount) * 100}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {Math.round(((loan.amount - loan.remainingBalance) / loan.amount) * 100)}% paid
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const FilterButton = ({ filter, title, count }) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        selectedFilter === filter && styles.filterButtonActive
      ]}
      onPress={() => setSelectedFilter(filter)}
    >
      <Text style={[
        styles.filterButtonText,
        selectedFilter === filter && styles.filterButtonTextActive
      ]}>
        {title} ({count})
      </Text>
    </TouchableOpacity>
  );

  const filteredLoans = getFilteredLoans();
  const activeLoanCount = loans.filter(l => l.status === 'active').length;
  const pendingLoanCount = loans.filter(l => l.status === 'pending').length;
  const completedLoanCount = loans.filter(l => l.status === 'completed').length;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Loans</Text>
        <TouchableOpacity 
          onPress={() => navigation.navigate('Calculator')}
          style={styles.addButton}
        >
          <Ionicons name="add" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.filtersContainer}
        contentContainerStyle={styles.filtersContent}
      >
        <FilterButton filter="all" title="All" count={loans.length} />
        <FilterButton filter="active" title="Active" count={activeLoanCount} />
        <FilterButton filter="pending" title="Pending" count={pendingLoanCount} />
        <FilterButton filter="completed" title="Completed" count={completedLoanCount} />
      </ScrollView>

      {/* Loans List */}
      <ScrollView
        style={styles.loansList}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
      >
        {filteredLoans.length > 0 ? (
          filteredLoans.map(loan => (
            <LoanCard key={loan.id} loan={loan} />
          ))
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color="#ccc" />
            <Text style={styles.emptyStateTitle}>No Loans Found</Text>
            <Text style={styles.emptyStateText}>
              {selectedFilter === 'all' 
                ? "You haven't applied for any loans yet."
                : `No ${selectedFilter} loans found.`
              }
            </Text>
            <TouchableOpacity 
              style={styles.emptyStateButton}
              onPress={() => navigation.navigate('Calculator')}
            >
              <Text style={styles.emptyStateButtonText}>Calculate Loan</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    backgroundColor: '#fff',
    paddingBottom: 10,
  },
  filtersContent: {
    paddingHorizontal: 20,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    marginRight: 12,
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  loansList: {
    flex: 1,
    padding: 20,
  },
  loanCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  loanTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  loanDate: {
    fontSize: 14,
    color: '#666',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  loanDetails: {
    marginBottom: 16,
  },
  loanDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  loanDetailLabel: {
    fontSize: 14,
    color: '#666',
  },
  loanDetailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e9ecef',
    borderRadius: 3,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#28a745',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default LoansScreen;
