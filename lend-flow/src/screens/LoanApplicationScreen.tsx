import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TextInput, But<PERSON>, Card, HelperText, Chip } from 'react-native-paper';
import { Picker } from '@react-native-picker/picker';
import { useAuth } from '../contexts/AuthContext';
import { mockLoanService } from '../services/mockService';
import { formatCurrency, calculateLoan } from '../utils/loanCalculator';
import { Loan } from '../types';

interface LoanApplicationData {
  amount: string;
  termMonths: number;
  purpose: string;
  monthlyIncome: string;
  employmentStatus: string;
  employerName: string;
  workExperience: string;
  bankName: string;
  accountNumber: string;
  accountType: string;
}

const LoanApplicationScreen = ({ navigation }: any) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [applicationData, setApplicationData] = useState<LoanApplicationData>({
    amount: '50000',
    termMonths: 24,
    purpose: '',
    monthlyIncome: '',
    employmentStatus: 'employed',
    employerName: '',
    workExperience: '',
    bankName: '',
    accountNumber: '',
    accountType: 'savings',
  });

  const [calculatedLoan, setCalculatedLoan] = useState<any>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const loanPurposes = [
    'Business expansion',
    'Home improvement',
    'Education',
    'Medical expenses',
    'Debt consolidation',
    'Emergency fund',
    'Vehicle purchase',
    'Wedding expenses',
    'Travel',
    'Other',
  ];

  const employmentStatuses = [
    { label: 'Employed (Full-time)', value: 'employed' },
    { label: 'Self-employed', value: 'self_employed' },
    { label: 'Contract worker', value: 'contract' },
    { label: 'Part-time', value: 'part_time' },
  ];

  const accountTypes = [
    { label: 'Savings Account', value: 'savings' },
    { label: 'Current Account', value: 'current' },
    { label: 'Cheque Account', value: 'cheque' },
  ];

  useEffect(() => {
    if (applicationData.amount && applicationData.termMonths) {
      calculateLoanDetails();
    }
  }, [applicationData.amount, applicationData.termMonths]);

  const calculateLoanDetails = () => {
    const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));
    if (!isNaN(amount) && amount > 0) {
      const calculation = calculateLoan(amount, applicationData.termMonths, 12); // 12% default rate
      setCalculatedLoan(calculation);
    }
  };

  const formatAmountInput = (text: string) => {
    const cleaned = text.replace(/[^0-9]/g, '');
    if (cleaned === '') return '';
    const number = parseInt(cleaned);
    return number.toLocaleString('en-ZA');
  };

  const handleAmountChange = (text: string) => {
    const formatted = formatAmountInput(text);
    setApplicationData(prev => ({ ...prev, amount: formatted }));
  };

  const validateStep = (stepNumber: number): boolean => {
    const newErrors: { [key: string]: string } = {};

    switch (stepNumber) {
      case 1:
        const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));
        if (!amount || amount < 5000) {
          newErrors.amount = 'Minimum loan amount is R 5,000';
        }
        if (amount > 500000) {
          newErrors.amount = 'Maximum loan amount is R 500,000';
        }
        if (!applicationData.purpose) {
          newErrors.purpose = 'Please select a loan purpose';
        }
        break;

      case 2:
        const income = parseFloat(applicationData.monthlyIncome.replace(/[^0-9.]/g, ''));
        if (!income || income < 3000) {
          newErrors.monthlyIncome = 'Minimum monthly income is R 3,000';
        }
        if (!applicationData.employerName.trim()) {
          newErrors.employerName = 'Employer name is required';
        }
        if (!applicationData.workExperience) {
          newErrors.workExperience = 'Work experience is required';
        }
        break;

      case 3:
        if (!applicationData.bankName.trim()) {
          newErrors.bankName = 'Bank name is required';
        }
        if (!applicationData.accountNumber.trim()) {
          newErrors.accountNumber = 'Account number is required';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  const handlePrevious = () => {
    setStep(step - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(3)) return;

    setLoading(true);
    try {
      const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));

      const loanData: Partial<Loan> = {
        userId: user?.id || '',
        amount,
        interestRate: 12,
        termMonths: applicationData.termMonths,
        monthlyPayment: calculatedLoan?.monthlyPayment || 0,
        totalAmount: calculatedLoan?.totalAmount || 0,
        purpose: applicationData.purpose,
        status: 'pending',
        remainingBalance: amount,
        paymentsCompleted: 0,
      };

      const newLoan = await mockLoanService.createLoan(loanData);

      if (newLoan) {
        Alert.alert(
          'Application Submitted!',
          'Your loan application has been submitted successfully. You will receive a notification once it has been reviewed.',
          [
            {
              text: 'View Application',
              onPress: () => navigation.navigate('Loans'),
            },
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to submit loan application. Please try again.');
      }
    } catch (error) {
      console.error('Loan application error:', error);
      Alert.alert('Error', 'Failed to submit loan application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStep1 = () => (
    <View>
      <Text style={styles.stepTitle}>Loan Details</Text>

      {/* Loan Amount */}
      <TextInput
        mode="outlined"
        label="Loan Amount"
        value={applicationData.amount}
        onChangeText={handleAmountChange}
        keyboardType="numeric"
        style={styles.input}
        left={<TextInput.Icon icon={() => <Text style={styles.currencyIcon}>R</Text>} />}
        error={!!errors.amount}
        theme={{ colors: { primary: '#007AFF' } }}
      />
      <HelperText type="error" visible={!!errors.amount}>
        {errors.amount}
      </HelperText>

      {/* Loan Term */}
      <Text style={styles.label}>Loan Term</Text>
      <View style={styles.pickerContainer}>
        <Picker
          selectedValue={applicationData.termMonths}
          onValueChange={(value) => setApplicationData(prev => ({ ...prev, termMonths: value }))}
          style={styles.picker}
        >
          {[6, 12, 18, 24, 36, 48, 60].map(months => (
            <Picker.Item
              key={months}
              label={`${months} months`}
              value={months}
            />
          ))}
        </Picker>
      </View>

      {/* Loan Purpose */}
      <Text style={styles.label}>Purpose of Loan</Text>
      <View style={styles.chipContainer}>
        {loanPurposes.map((purpose) => (
          <Chip
            key={purpose}
            selected={applicationData.purpose === purpose}
            onPress={() => setApplicationData(prev => ({ ...prev, purpose }))}
            style={[
              styles.chip,
              applicationData.purpose === purpose && styles.chipSelected
            ]}
            textStyle={[
              styles.chipText,
              applicationData.purpose === purpose && styles.chipTextSelected
            ]}
          >
            {purpose}
          </Chip>
        ))}
      </View>
      <HelperText type="error" visible={!!errors.purpose}>
        {errors.purpose}
      </HelperText>

      {/* Loan Calculation */}
      {calculatedLoan && (
        <Card style={styles.calculationCard}>
          <Card.Content>
            <Text style={styles.calculationTitle}>Loan Summary</Text>
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>Monthly Payment:</Text>
              <Text style={styles.calculationValue}>
                {formatCurrency(calculatedLoan.monthlyPayment)}
              </Text>
            </View>
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>Total Interest:</Text>
              <Text style={styles.calculationValue}>
                {formatCurrency(calculatedLoan.totalInterest)}
              </Text>
            </View>
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>Total Amount:</Text>
              <Text style={styles.calculationValue}>
                {formatCurrency(calculatedLoan.totalAmount)}
              </Text>
            </View>
          </Card.Content>
        </Card>
      )}
    </View>
  );

  const renderStep2 = () => (
    <View>
      <Text style={styles.stepTitle}>Employment Information</Text>

      {/* Monthly Income */}
      <TextInput
        mode="outlined"
        label="Monthly Income"
        value={applicationData.monthlyIncome}
        onChangeText={(text) => {
          const formatted = formatAmountInput(text);
          setApplicationData(prev => ({ ...prev, monthlyIncome: formatted }));
        }}
        keyboardType="numeric"
        style={styles.input}
        left={<TextInput.Icon icon={() => <Text style={styles.currencyIcon}>R</Text>} />}
        error={!!errors.monthlyIncome}
        theme={{ colors: { primary: '#007AFF' } }}
      />
      <HelperText type="error" visible={!!errors.monthlyIncome}>
        {errors.monthlyIncome}
      </HelperText>

      {/* Employment Status */}
      <Text style={styles.label}>Employment Status</Text>
      <View style={styles.pickerContainer}>
        <Picker
          selectedValue={applicationData.employmentStatus}
          onValueChange={(value) => setApplicationData(prev => ({ ...prev, employmentStatus: value }))}
          style={styles.picker}
        >
          {employmentStatuses.map(status => (
            <Picker.Item
              key={status.value}
              label={status.label}
              value={status.value}
            />
          ))}
        </Picker>
      </View>

      {/* Employer Name */}
      <TextInput
        mode="outlined"
        label="Employer/Company Name"
        value={applicationData.employerName}
        onChangeText={(text) => setApplicationData(prev => ({ ...prev, employerName: text }))}
        style={styles.input}
        error={!!errors.employerName}
        theme={{ colors: { primary: '#007AFF' } }}
      />
      <HelperText type="error" visible={!!errors.employerName}>
        {errors.employerName}
      </HelperText>

      {/* Work Experience */}
      <Text style={styles.label}>Years of Work Experience</Text>
      <View style={styles.pickerContainer}>
        <Picker
          selectedValue={applicationData.workExperience}
          onValueChange={(value) => setApplicationData(prev => ({ ...prev, workExperience: value }))}
          style={styles.picker}
        >
          <Picker.Item label="Select experience" value="" />
          <Picker.Item label="Less than 1 year" value="<1" />
          <Picker.Item label="1-2 years" value="1-2" />
          <Picker.Item label="3-5 years" value="3-5" />
          <Picker.Item label="6-10 years" value="6-10" />
          <Picker.Item label="More than 10 years" value="10+" />
        </Picker>
      </View>
      <HelperText type="error" visible={!!errors.workExperience}>
        {errors.workExperience}
      </HelperText>
    </View>
  );

  const renderStep3 = () => (
    <View>
      <Text style={styles.stepTitle}>Banking Information</Text>

      {/* Bank Name */}
      <TextInput
        mode="outlined"
        label="Bank Name"
        value={applicationData.bankName}
        onChangeText={(text) => setApplicationData(prev => ({ ...prev, bankName: text }))}
        style={styles.input}
        error={!!errors.bankName}
        theme={{ colors: { primary: '#007AFF' } }}
      />
      <HelperText type="error" visible={!!errors.bankName}>
        {errors.bankName}
      </HelperText>

      {/* Account Number */}
      <TextInput
        mode="outlined"
        label="Account Number"
        value={applicationData.accountNumber}
        onChangeText={(text) => setApplicationData(prev => ({ ...prev, accountNumber: text }))}
        keyboardType="numeric"
        style={styles.input}
        error={!!errors.accountNumber}
        theme={{ colors: { primary: '#007AFF' } }}
      />
      <HelperText type="error" visible={!!errors.accountNumber}>
        {errors.accountNumber}
      </HelperText>

      {/* Account Type */}
      <Text style={styles.label}>Account Type</Text>
      <View style={styles.pickerContainer}>
        <Picker
          selectedValue={applicationData.accountType}
          onValueChange={(value) => setApplicationData(prev => ({ ...prev, accountType: value }))}
          style={styles.picker}
        >
          {accountTypes.map(type => (
            <Picker.Item
              key={type.value}
              label={type.label}
              value={type.value}
            />
          ))}
        </Picker>
      </View>

      {/* Final Summary */}
      {calculatedLoan && (
        <Card style={styles.summaryCard}>
          <Card.Content>
            <Text style={styles.summaryTitle}>Application Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Loan Amount:</Text>
              <Text style={styles.summaryValue}>
                {formatCurrency(parseFloat(applicationData.amount.replace(/[^0-9.]/g, '')))}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Term:</Text>
              <Text style={styles.summaryValue}>{applicationData.termMonths} months</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Purpose:</Text>
              <Text style={styles.summaryValue}>{applicationData.purpose}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Monthly Payment:</Text>
              <Text style={[styles.summaryValue, styles.highlightValue]}>
                {formatCurrency(calculatedLoan.monthlyPayment)}
              </Text>
            </View>
          </Card.Content>
        </Card>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Loan Application</Text>
            <Text style={styles.subtitle}>Step {step} of 3</Text>
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            {[1, 2, 3].map((stepNumber) => (
              <View
                key={stepNumber}
                style={[
                  styles.progressStep,
                  stepNumber <= step && styles.progressStepActive,
                ]}
              />
            ))}
          </View>

          {/* Step Content */}
          <View style={styles.content}>
            {step === 1 && renderStep1()}
            {step === 2 && renderStep2()}
            {step === 3 && renderStep3()}
          </View>

          {/* Navigation Buttons */}
          <View style={styles.buttonContainer}>
            {step > 1 && (
              <Button
                mode="outlined"
                onPress={handlePrevious}
                style={[styles.button, styles.backButton]}
                labelStyle={styles.backButtonText}
              >
                Previous
              </Button>
            )}

            {step < 3 ? (
              <Button
                mode="contained"
                onPress={handleNext}
                style={[styles.button, styles.nextButton]}
                labelStyle={styles.nextButtonText}
              >
                Next
              </Button>
            ) : (
              <Button
                mode="contained"
                onPress={handleSubmit}
                loading={loading}
                disabled={loading}
                style={[styles.button, styles.submitButton]}
                labelStyle={styles.submitButtonText}
              >
                Submit Application
              </Button>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  progressStep: {
    width: 60,
    height: 4,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 4,
    borderRadius: 2,
  },
  progressStepActive: {
    backgroundColor: '#007AFF',
  },
  content: {
    padding: 20,
    paddingTop: 0,
  },
  stepTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    marginBottom: 8,
    backgroundColor: '#fff',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  currencyIcon: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  picker: {
    height: 50,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  chip: {
    margin: 4,
    backgroundColor: '#f0f0f0',
  },
  chipSelected: {
    backgroundColor: '#007AFF',
  },
  chipText: {
    color: '#333',
  },
  chipTextSelected: {
    color: '#fff',
  },
  calculationCard: {
    marginTop: 20,
    backgroundColor: '#fff',
    elevation: 2,
  },
  calculationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  calculationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  calculationLabel: {
    fontSize: 14,
    color: '#666',
  },
  calculationValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  summaryCard: {
    marginTop: 20,
    backgroundColor: '#e3f2fd',
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#1976d2',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1976d2',
  },
  highlightValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 10,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
  backButton: {
    borderColor: '#007AFF',
  },
  backButtonText: {
    color: '#007AFF',
  },
  nextButton: {
    backgroundColor: '#007AFF',
  },
  nextButtonText: {
    color: '#fff',
  },
  submitButton: {
    backgroundColor: '#4CAF50',
  },
  submitButtonText: {
    color: '#fff',
  },
});

export default LoanApplicationScreen;
