import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TextInput, Button, Card, HelperText, RadioButton } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { mockLoanService } from '../services/mockService';
import { apiLoanService } from '../services/apiService';
import { formatCurrency } from '../utils/loanCalculator';
import { Loan } from '../types';

interface LoanApplicationData {
  amount: string;
  termDays: number;
  paymentMethod: 'cash_send' | 'eft';
  repaymentDate: string;
}

interface LoanSettings {
  minAmount: number;
  maxAmount: number;
  minTermDays: number;
  maxTermDays: number;
  interestRate: number;
  processingFee: number;
  paymentMethods: string[];
}

const LoanApplicationScreen = ({ navigation }: any) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [loanSettings, setLoanSettings] = useState<LoanSettings | null>(null);
  const [applicationData, setApplicationData] = useState<LoanApplicationData>({
    amount: '1000',
    termDays: 7,
    paymentMethod: 'cash_send',
    repaymentDate: '',
  });

  const [calculatedLoan, setCalculatedLoan] = useState<any>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Load loan settings on component mount
  useEffect(() => {
    loadLoanSettings();
  }, []);

  const loadLoanSettings = async () => {
    try {
      const settings = await apiLoanService.getLoanSettings();
      setLoanSettings(settings);

      // Set default repayment date (7 days from today)
      const defaultDate = new Date();
      defaultDate.setDate(defaultDate.getDate() + 7);
      setApplicationData(prev => ({
        ...prev,
        repaymentDate: defaultDate.toISOString().split('T')[0],
      }));
    } catch (error) {
      console.error('Failed to load loan settings:', error);
    }
  };

  // Calculate loan details when amount or term changes
  useEffect(() => {
    if (applicationData.amount && applicationData.termDays && loanSettings) {
      calculateLoanDetails();
    }
  }, [applicationData.amount, applicationData.termDays, loanSettings]);

  const calculateLoanDetails = () => {
    if (!loanSettings) return;

    const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));
    if (!isNaN(amount) && amount > 0) {
      const interestAmount = (amount * loanSettings.interestRate / 100);
      const totalAmount = amount + interestAmount + loanSettings.processingFee;

      setCalculatedLoan({
        principal: amount,
        interestAmount,
        processingFee: loanSettings.processingFee,
        totalAmount,
        termDays: applicationData.termDays,
        interestRate: loanSettings.interestRate,
      });
    }
  };

  const formatAmountInput = (text: string) => {
    const cleaned = text.replace(/[^0-9]/g, '');
    if (cleaned === '') return '';
    const number = parseInt(cleaned);
    return number.toLocaleString('en-ZA');
  };

  const handleAmountChange = (text: string) => {
    const formatted = formatAmountInput(text);
    setApplicationData(prev => ({ ...prev, amount: formatted }));
  };

  const getMinDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  const getMaxDate = () => {
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    return maxDate.toISOString().split('T')[0];
  };

  const validateStep = (stepNumber: number): boolean => {
    if (!loanSettings) return false;

    const newErrors: { [key: string]: string } = {};

    switch (stepNumber) {
      case 1:
        const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));
        if (!amount || amount < loanSettings.minAmount) {
          newErrors.amount = `Minimum loan amount is ${formatCurrency(loanSettings.minAmount)}`;
        }
        if (amount > loanSettings.maxAmount) {
          newErrors.amount = `Maximum loan amount is ${formatCurrency(loanSettings.maxAmount)}`;
        }
        break;

      case 2:
        if (!applicationData.paymentMethod) {
          newErrors.paymentMethod = 'Please select a payment method';
        }
        break;

      case 3:
        if (!applicationData.repaymentDate) {
          newErrors.repaymentDate = 'Please select a repayment date';
        } else {
          const selectedDate = new Date(applicationData.repaymentDate);
          const today = new Date();
          const maxDate = new Date();
          maxDate.setDate(today.getDate() + 30);

          if (selectedDate <= today) {
            newErrors.repaymentDate = 'Repayment date must be in the future';
          }
          if (selectedDate > maxDate) {
            newErrors.repaymentDate = 'Repayment date cannot be more than 30 days from today';
          }
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  const handlePrevious = () => {
    setStep(step - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(3) || !calculatedLoan) return;

    setLoading(true);
    try {
      const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));

      const loanData: Partial<Loan> = {
        userId: user?.id || '',
        amount,
        interestRate: loanSettings?.interestRate || 30,
        termMonths: Math.ceil(applicationData.termDays / 30), // Convert days to months for compatibility
        monthlyPayment: calculatedLoan.totalAmount, // For mashonisa, it's a single payment
        totalAmount: calculatedLoan.totalAmount,
        purpose: 'Short-term loan', // Default purpose for mashonisa
        status: 'pending',
        remainingBalance: amount,
        paymentsCompleted: 0,
        repaymentDate: applicationData.repaymentDate,
        paymentMethod: applicationData.paymentMethod,
      };

      const newLoan = await mockLoanService.createLoan(loanData);

      if (newLoan) {
        Alert.alert(
          'Application Submitted!',
          'Your loan application has been submitted successfully. You will receive a notification once it has been reviewed.',
          [
            {
              text: 'View Applications',
              onPress: () => navigation.navigate('Loans'),
            },
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to submit loan application. Please try again.');
      }
    } catch (error) {
      console.error('Loan application error:', error);
      Alert.alert('Error', 'Failed to submit loan application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Step 1: Enter Amount
  const renderStep1 = () => (
    <View>
      <Text style={styles.stepTitle}>Loan Amount</Text>
      <Text style={styles.stepDescription}>
        Enter the amount you need. Quick approval for amounts up to {loanSettings ? formatCurrency(loanSettings.maxAmount) : 'R 50,000'}.
      </Text>

      {/* Loan Amount */}
      <TextInput
        mode="outlined"
        label="Loan Amount"
        value={applicationData.amount}
        onChangeText={handleAmountChange}
        keyboardType="numeric"
        style={styles.input}
        left={<TextInput.Icon icon={() => <Text style={styles.currencyIcon}>R</Text>} />}
        error={!!errors.amount}
        theme={{ colors: { primary: '#007AFF' } }}
      />
      <HelperText type="error" visible={!!errors.amount}>
        {errors.amount}
      </HelperText>

      {loanSettings && (
        <Text style={styles.helperText}>
          Amount range: {formatCurrency(loanSettings.minAmount)} - {formatCurrency(loanSettings.maxAmount)}
        </Text>
      )}

      {/* Term Selection */}
      <Text style={styles.label}>Repayment Period</Text>
      <View style={styles.termContainer}>
        {[7, 14, 21, 30].map((days) => (
          <TouchableOpacity
            key={days}
            style={[
              styles.termButton,
              applicationData.termDays === days && styles.termButtonSelected
            ]}
            onPress={() => setApplicationData(prev => ({ ...prev, termDays: days }))}
          >
            <Text style={[
              styles.termButtonText,
              applicationData.termDays === days && styles.termButtonTextSelected
            ]}>
              {days} days
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Quick Calculation Preview */}
      {calculatedLoan && (
        <Card style={styles.previewCard}>
          <Card.Content>
            <Text style={styles.previewTitle}>Quick Preview</Text>
            <View style={styles.previewRow}>
              <Text style={styles.previewLabel}>You borrow:</Text>
              <Text style={styles.previewValue}>{formatCurrency(calculatedLoan.principal)}</Text>
            </View>
            <View style={styles.previewRow}>
              <Text style={styles.previewLabel}>You repay:</Text>
              <Text style={[styles.previewValue, styles.previewHighlight]}>
                {formatCurrency(calculatedLoan.totalAmount)}
              </Text>
            </View>
            <View style={styles.previewRow}>
              <Text style={styles.previewLabel}>In:</Text>
              <Text style={styles.previewValue}>{applicationData.termDays} days</Text>
            </View>
          </Card.Content>
        </Card>
      )}
    </View>
  );

  // Step 2: Select Payment Method
  const renderStep2 = () => (
    <View>
      <Text style={styles.stepTitle}>Payment Method</Text>
      <Text style={styles.stepDescription}>
        Choose how you'd like to receive your loan funds.
      </Text>

      <View style={styles.paymentMethodContainer}>
        {/* Cash Send Option */}
        <TouchableOpacity
          style={[
            styles.paymentMethodCard,
            applicationData.paymentMethod === 'cash_send' && styles.paymentMethodCardSelected
          ]}
          onPress={() => setApplicationData(prev => ({ ...prev, paymentMethod: 'cash_send' }))}
        >
          <View style={styles.paymentMethodHeader}>
            <Ionicons
              name="cash"
              size={32}
              color={applicationData.paymentMethod === 'cash_send' ? '#007AFF' : '#666'}
            />
            <RadioButton
              value="cash_send"
              status={applicationData.paymentMethod === 'cash_send' ? 'checked' : 'unchecked'}
              onPress={() => setApplicationData(prev => ({ ...prev, paymentMethod: 'cash_send' }))}
              color="#007AFF"
            />
          </View>
          <Text style={[
            styles.paymentMethodTitle,
            applicationData.paymentMethod === 'cash_send' && styles.paymentMethodTitleSelected
          ]}>
            Cash Send
          </Text>
          <Text style={styles.paymentMethodDescription}>
            Instant cash pickup at any participating retailer. No bank account needed.
          </Text>
          <View style={styles.paymentMethodFeatures}>
            <Text style={styles.featureText}>• Instant availability</Text>
            <Text style={styles.featureText}>• Pick up anywhere</Text>
            <Text style={styles.featureText}>• No bank account required</Text>
          </View>
        </TouchableOpacity>

        {/* EFT Option */}
        <TouchableOpacity
          style={[
            styles.paymentMethodCard,
            applicationData.paymentMethod === 'eft' && styles.paymentMethodCardSelected
          ]}
          onPress={() => setApplicationData(prev => ({ ...prev, paymentMethod: 'eft' }))}
        >
          <View style={styles.paymentMethodHeader}>
            <Ionicons
              name="card"
              size={32}
              color={applicationData.paymentMethod === 'eft' ? '#007AFF' : '#666'}
            />
            <RadioButton
              value="eft"
              status={applicationData.paymentMethod === 'eft' ? 'checked' : 'unchecked'}
              onPress={() => setApplicationData(prev => ({ ...prev, paymentMethod: 'eft' }))}
              color="#007AFF"
            />
          </View>
          <Text style={[
            styles.paymentMethodTitle,
            applicationData.paymentMethod === 'eft' && styles.paymentMethodTitleSelected
          ]}>
            Bank Transfer (EFT)
          </Text>
          <Text style={styles.paymentMethodDescription}>
            Direct transfer to your bank account. Usually processed within 2-4 hours.
          </Text>
          <View style={styles.paymentMethodFeatures}>
            <Text style={styles.featureText}>• Direct to your account</Text>
            <Text style={styles.featureText}>• Secure transfer</Text>
            <Text style={styles.featureText}>• 2-4 hour processing</Text>
          </View>
        </TouchableOpacity>
      </View>

      <HelperText type="error" visible={!!errors.paymentMethod}>
        {errors.paymentMethod}
      </HelperText>
    </View>
  );

  // Step 3: Repayment Date and Summary
  const renderStep3 = () => (
    <View>
      <Text style={styles.stepTitle}>Repayment Date</Text>
      <Text style={styles.stepDescription}>
        Choose when you'll repay the loan (maximum 30 days from today).
      </Text>

      {/* Repayment Date */}
      <Text style={styles.label}>Select Repayment Date</Text>
      <TextInput
        mode="outlined"
        label="Repayment Date"
        value={applicationData.repaymentDate}
        onChangeText={(date) => setApplicationData(prev => ({ ...prev, repaymentDate: date }))}
        style={styles.input}
        right={<TextInput.Icon icon="calendar" />}
        error={!!errors.repaymentDate}
        theme={{ colors: { primary: '#007AFF' } }}
        placeholder="YYYY-MM-DD"
      />
      <HelperText type="error" visible={!!errors.repaymentDate}>
        {errors.repaymentDate}
      </HelperText>

      <Text style={styles.helperText}>
        Date range: {getMinDate()} to {getMaxDate()}
      </Text>

      {/* Quick Date Options */}
      <Text style={styles.label}>Quick Options</Text>
      <View style={styles.quickDateContainer}>
        {[7, 14, 21, 30].map((days) => {
          const date = new Date();
          date.setDate(date.getDate() + days);
          const dateString = date.toISOString().split('T')[0];

          return (
            <TouchableOpacity
              key={days}
              style={[
                styles.quickDateButton,
                applicationData.repaymentDate === dateString && styles.quickDateButtonSelected
              ]}
              onPress={() => setApplicationData(prev => ({ ...prev, repaymentDate: dateString }))}
            >
              <Text style={[
                styles.quickDateText,
                applicationData.repaymentDate === dateString && styles.quickDateTextSelected
              ]}>
                {days} days
              </Text>
              <Text style={[
                styles.quickDateSubtext,
                applicationData.repaymentDate === dateString && styles.quickDateTextSelected
              ]}>
                {date.toLocaleDateString('en-ZA', { month: 'short', day: 'numeric' })}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Final Summary */}
      {calculatedLoan && (
        <Card style={styles.summaryCard}>
          <Card.Content>
            <Text style={styles.summaryTitle}>Loan Summary</Text>

            <View style={styles.summarySection}>
              <Text style={styles.sectionTitle}>Loan Details</Text>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Amount:</Text>
                <Text style={styles.summaryValue}>{formatCurrency(calculatedLoan.principal)}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Interest ({loanSettings?.interestRate}%):</Text>
                <Text style={styles.summaryValue}>{formatCurrency(calculatedLoan.interestAmount)}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Processing Fee:</Text>
                <Text style={styles.summaryValue}>{formatCurrency(calculatedLoan.processingFee)}</Text>
              </View>
              <View style={[styles.summaryRow, styles.totalRow]}>
                <Text style={styles.summaryLabelBold}>Total to Repay:</Text>
                <Text style={styles.summaryValueBold}>{formatCurrency(calculatedLoan.totalAmount)}</Text>
              </View>
            </View>

            <View style={styles.summarySection}>
              <Text style={styles.sectionTitle}>Payment Details</Text>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Payment Method:</Text>
                <Text style={styles.summaryValue}>
                  {applicationData.paymentMethod === 'cash_send' ? 'Cash Send' : 'Bank Transfer'}
                </Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Repayment Date:</Text>
                <Text style={styles.summaryValue}>
                  {applicationData.repaymentDate ?
                    new Date(applicationData.repaymentDate).toLocaleDateString('en-ZA', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) : 'Not selected'
                  }
                </Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Term:</Text>
                <Text style={styles.summaryValue}>{applicationData.termDays} days</Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Quick Loan Application</Text>
            <Text style={styles.subtitle}>Step {step} of 3 - Fast & Simple</Text>
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            {[
              { step: 1, label: 'Amount' },
              { step: 2, label: 'Payment' },
              { step: 3, label: 'Summary' }
            ].map((item) => (
              <View key={item.step} style={styles.progressItem}>
                <View
                  style={[
                    styles.progressStep,
                    item.step <= step && styles.progressStepActive,
                  ]}
                />
                <Text style={[
                  styles.progressLabel,
                  item.step <= step && styles.progressLabelActive,
                ]}>
                  {item.label}
                </Text>
              </View>
            ))}
          </View>

          {/* Step Content */}
          <View style={styles.content}>
            {step === 1 && renderStep1()}
            {step === 2 && renderStep2()}
            {step === 3 && renderStep3()}
          </View>

          {/* Navigation Buttons */}
          <View style={styles.buttonContainer}>
            {step > 1 && (
              <Button
                mode="outlined"
                onPress={handlePrevious}
                style={[styles.button, styles.backButton]}
                labelStyle={styles.backButtonText}
              >
                Previous
              </Button>
            )}

            {step < 3 ? (
              <Button
                mode="contained"
                onPress={handleNext}
                style={[styles.button, styles.nextButton]}
                labelStyle={styles.nextButtonText}
              >
                Next
              </Button>
            ) : (
              <Button
                mode="contained"
                onPress={handleSubmit}
                loading={loading}
                disabled={loading}
                style={[styles.button, styles.submitButton]}
                labelStyle={styles.submitButtonText}
              >
                Submit Application
              </Button>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 40,
    marginBottom: 20,
  },
  progressItem: {
    alignItems: 'center',
    flex: 1,
  },
  progressStep: {
    width: 40,
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressStepActive: {
    backgroundColor: '#007AFF',
  },
  progressLabel: {
    fontSize: 12,
    color: '#999',
    fontWeight: '500',
  },
  progressLabelActive: {
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    padding: 20,
    paddingTop: 0,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  input: {
    marginBottom: 8,
    backgroundColor: '#fff',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    marginTop: 20,
  },
  currencyIcon: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  helperText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  // Term selection styles
  termContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  termButton: {
    flex: 1,
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  termButtonSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  termButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  termButtonTextSelected: {
    color: '#007AFF',
  },
  // Preview card styles
  previewCard: {
    marginTop: 20,
    backgroundColor: '#e8f5e8',
    elevation: 2,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2e7d32',
    marginBottom: 12,
    textAlign: 'center',
  },
  previewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  previewLabel: {
    fontSize: 14,
    color: '#2e7d32',
  },
  previewValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2e7d32',
  },
  previewHighlight: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1b5e20',
  },
  // Payment method styles
  paymentMethodContainer: {
    marginBottom: 20,
  },
  paymentMethodCard: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  paymentMethodCardSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  paymentMethodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  paymentMethodTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  paymentMethodTitleSelected: {
    color: '#007AFF',
  },
  paymentMethodDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    lineHeight: 20,
  },
  paymentMethodFeatures: {
    marginTop: 8,
  },
  featureText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  // Quick date selection styles
  quickDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  quickDateButton: {
    flex: 1,
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  quickDateButtonSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  quickDateText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  quickDateTextSelected: {
    color: '#007AFF',
  },
  quickDateSubtext: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  // Summary card styles
  summaryCard: {
    marginTop: 20,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  summarySection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingBottom: 4,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingVertical: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'right',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    marginTop: 8,
    paddingTop: 12,
  },
  summaryLabelBold: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  summaryValueBold: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    textAlign: 'right',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 10,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
  backButton: {
    borderColor: '#007AFF',
  },
  backButtonText: {
    color: '#007AFF',
  },
  nextButton: {
    backgroundColor: '#007AFF',
  },
  nextButtonText: {
    color: '#fff',
  },
  submitButton: {
    backgroundColor: '#4CAF50',
  },
  submitButtonText: {
    color: '#fff',
  },
});

export default LoanApplicationScreen;
