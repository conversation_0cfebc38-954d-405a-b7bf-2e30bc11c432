import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Dimensions,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TextInput, Button, Card, HelperText } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { mockLoanService } from '../services/mockService';
import { apiLoanService } from '../services/apiService';
import { formatCurrency } from '../utils/loanCalculator';
import { Loan } from '../types';

const { width } = Dimensions.get('window');

interface LoanApplicationData {
  amount: string;
  termDays: number;
  paymentMethod: 'cash_send' | 'eft';
  repaymentDate: string;
}

interface LoanSettings {
  minAmount: number;
  maxAmount: number;
  minTermDays: number;
  maxTermDays: number;
  interestRate: number;
  processingFee: number;
  paymentMethods: string[];
}

const LoanApplicationScreen = ({ navigation }: any) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [loanSettings, setLoanSettings] = useState<LoanSettings | null>(null);
  const [applicationData, setApplicationData] = useState<LoanApplicationData>({
    amount: '1000',
    termDays: 7,
    paymentMethod: 'cash_send',
    repaymentDate: '',
  });

  const [calculatedLoan, setCalculatedLoan] = useState<any>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Load loan settings on component mount
  useEffect(() => {
    loadLoanSettings();
  }, []);

  const loadLoanSettings = async () => {
    try {
      const settings = await apiLoanService.getLoanSettings();
      setLoanSettings(settings);

      // Set default repayment date (7 days from today)
      const defaultDate = new Date();
      defaultDate.setDate(defaultDate.getDate() + 7);
      setApplicationData(prev => ({
        ...prev,
        repaymentDate: defaultDate.toISOString().split('T')[0],
      }));
    } catch (error) {
      console.error('Failed to load loan settings:', error);
    }
  };

  // Calculate loan details when amount or term changes
  useEffect(() => {
    if (applicationData.amount && applicationData.termDays && loanSettings) {
      calculateLoanDetails();
    }
  }, [applicationData.amount, applicationData.termDays, loanSettings]);

  const calculateLoanDetails = () => {
    if (!loanSettings) return;

    const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));
    if (!isNaN(amount) && amount > 0) {
      const interestAmount = (amount * loanSettings.interestRate / 100);
      const totalAmount = amount + interestAmount + loanSettings.processingFee;

      setCalculatedLoan({
        principal: amount,
        interestAmount,
        processingFee: loanSettings.processingFee,
        totalAmount,
        termDays: applicationData.termDays,
        interestRate: loanSettings.interestRate,
      });
    }
  };

  const formatAmountInput = (text: string) => {
    const cleaned = text.replace(/[^0-9]/g, '');
    if (cleaned === '') return '';
    const number = parseInt(cleaned);
    return number.toLocaleString('en-ZA');
  };

  const handleAmountChange = (text: string) => {
    const formatted = formatAmountInput(text);
    setApplicationData(prev => ({ ...prev, amount: formatted }));
  };

  const getMinDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  const getMaxDate = () => {
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    return maxDate.toISOString().split('T')[0];
  };

  const validateStep = (stepNumber: number): boolean => {
    if (!loanSettings) return false;

    const newErrors: { [key: string]: string } = {};

    switch (stepNumber) {
      case 1:
        const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));
        if (!amount || amount < loanSettings.minAmount) {
          newErrors.amount = `Minimum loan amount is ${formatCurrency(loanSettings.minAmount)}`;
        }
        if (amount > loanSettings.maxAmount) {
          newErrors.amount = `Maximum loan amount is ${formatCurrency(loanSettings.maxAmount)}`;
        }
        break;

      case 2:
        if (!applicationData.paymentMethod) {
          newErrors.paymentMethod = 'Please select a payment method';
        }
        break;

      case 3:
        if (!applicationData.repaymentDate) {
          newErrors.repaymentDate = 'Please select a repayment date';
        } else {
          const selectedDate = new Date(applicationData.repaymentDate);
          const today = new Date();
          const maxDate = new Date();
          maxDate.setDate(today.getDate() + 30);

          if (selectedDate <= today) {
            newErrors.repaymentDate = 'Repayment date must be in the future';
          }
          if (selectedDate > maxDate) {
            newErrors.repaymentDate = 'Repayment date cannot be more than 30 days from today';
          }
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  const handlePrevious = () => {
    setStep(step - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(3) || !calculatedLoan) return;

    setLoading(true);
    try {
      const amount = parseFloat(applicationData.amount.replace(/[^0-9.]/g, ''));

      const loanData: Partial<Loan> = {
        userId: user?.id || '',
        amount,
        interestRate: loanSettings?.interestRate || 30,
        termMonths: Math.ceil(applicationData.termDays / 30), // Convert days to months for compatibility
        monthlyPayment: calculatedLoan.totalAmount, // For mashonisa, it's a single payment
        totalAmount: calculatedLoan.totalAmount,
        purpose: 'Short-term loan', // Default purpose for mashonisa
        status: 'pending',
        remainingBalance: amount,
        paymentsCompleted: 0,
        repaymentDate: applicationData.repaymentDate,
        paymentMethod: applicationData.paymentMethod,
      };

      const newLoan = await mockLoanService.createLoan(loanData);

      if (newLoan) {
        Alert.alert(
          'Application Submitted!',
          'Your loan application has been submitted successfully. You will receive a notification once it has been reviewed.',
          [
            {
              text: 'View Applications',
              onPress: () => navigation.navigate('Loans'),
            },
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to submit loan application. Please try again.');
      }
    } catch (error) {
      console.error('Loan application error:', error);
      Alert.alert('Error', 'Failed to submit loan application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Step 1: Enter Amount - Beautiful Modern Design
  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      {/* Header with Icon */}
      <View style={styles.stepHeader}>
        <View style={styles.iconContainer}>
          <Ionicons name="cash-outline" size={32} color="#007AFF" />
        </View>
        <Text style={styles.stepTitle}>How much do you need?</Text>
        <Text style={styles.stepDescription}>
          Get instant cash from {loanSettings ? formatCurrency(loanSettings.minAmount) : 'R 500'} to {loanSettings ? formatCurrency(loanSettings.maxAmount) : 'R 50,000'}
        </Text>
      </View>

      {/* Amount Input with Beautiful Design */}
      <View style={styles.amountInputContainer}>
        <Text style={styles.amountLabel}>Loan Amount</Text>
        <View style={styles.amountInputWrapper}>
          <Text style={styles.currencySymbol}>R</Text>
          <TextInput
            value={applicationData.amount}
            onChangeText={handleAmountChange}
            keyboardType="numeric"
            style={styles.amountInput}
            placeholder="0"
            placeholderTextColor="#999"
          />
        </View>
        {errors.amount && (
          <Text style={styles.errorText}>{errors.amount}</Text>
        )}
      </View>

      {/* Quick Amount Buttons */}
      <View style={styles.quickAmountContainer}>
        <Text style={styles.quickAmountLabel}>Quick Select</Text>
        <View style={styles.quickAmountButtons}>
          {[1000, 2500, 5000, 10000].map((amount) => (
            <TouchableOpacity
              key={amount}
              style={[
                styles.quickAmountButton,
                applicationData.amount === amount.toLocaleString('en-ZA') && styles.quickAmountButtonSelected
              ]}
              onPress={() => setApplicationData(prev => ({ ...prev, amount: amount.toLocaleString('en-ZA') }))}
            >
              <Text style={[
                styles.quickAmountButtonText,
                applicationData.amount === amount.toLocaleString('en-ZA') && styles.quickAmountButtonTextSelected
              ]}>
                {formatCurrency(amount)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Term Selection with Beautiful Cards */}
      <View style={styles.termSelectionContainer}>
        <Text style={styles.termLabel}>When will you repay?</Text>
        <View style={styles.termCards}>
          {[
            { days: 7, label: '1 Week', popular: false },
            { days: 14, label: '2 Weeks', popular: true },
            { days: 21, label: '3 Weeks', popular: false },
            { days: 30, label: '1 Month', popular: false }
          ].map((term) => (
            <TouchableOpacity
              key={term.days}
              style={[
                styles.termCard,
                applicationData.termDays === term.days && styles.termCardSelected,
                term.popular && styles.termCardPopular
              ]}
              onPress={() => setApplicationData(prev => ({ ...prev, termDays: term.days }))}
            >
              {term.popular && (
                <View style={styles.popularBadge}>
                  <Text style={styles.popularBadgeText}>POPULAR</Text>
                </View>
              )}
              <Text style={[
                styles.termCardDays,
                applicationData.termDays === term.days && styles.termCardDaysSelected
              ]}>
                {term.days}
              </Text>
              <Text style={[
                styles.termCardLabel,
                applicationData.termDays === term.days && styles.termCardLabelSelected
              ]}>
                {term.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Beautiful Preview Card */}
      {calculatedLoan && (
        <LinearGradient
          colors={['#4CAF50', '#45a049']}
          style={styles.previewGradientCard}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.previewCardContent}>
            <View style={styles.previewHeader}>
              <Ionicons name="calculator-outline" size={24} color="white" />
              <Text style={styles.previewTitle}>Loan Preview</Text>
            </View>

            <View style={styles.previewAmounts}>
              <View style={styles.previewAmountItem}>
                <Text style={styles.previewAmountLabel}>You get</Text>
                <Text style={styles.previewAmountValue}>{formatCurrency(calculatedLoan.principal)}</Text>
              </View>
              <Ionicons name="arrow-forward" size={20} color="rgba(255,255,255,0.8)" />
              <View style={styles.previewAmountItem}>
                <Text style={styles.previewAmountLabel}>You repay</Text>
                <Text style={styles.previewAmountValueHighlight}>{formatCurrency(calculatedLoan.totalAmount)}</Text>
              </View>
            </View>

            <View style={styles.previewDetails}>
              <View style={styles.previewDetailItem}>
                <Text style={styles.previewDetailLabel}>Interest</Text>
                <Text style={styles.previewDetailValue}>{formatCurrency(calculatedLoan.interestAmount)}</Text>
              </View>
              <View style={styles.previewDetailItem}>
                <Text style={styles.previewDetailLabel}>Fee</Text>
                <Text style={styles.previewDetailValue}>{formatCurrency(calculatedLoan.processingFee)}</Text>
              </View>
              <View style={styles.previewDetailItem}>
                <Text style={styles.previewDetailLabel}>Term</Text>
                <Text style={styles.previewDetailValue}>{applicationData.termDays} days</Text>
              </View>
            </View>
          </View>
        </LinearGradient>
      )}
    </View>
  );

  // Step 2: Select Payment Method - Beautiful Design
  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      {/* Header with Icon */}
      <View style={styles.stepHeader}>
        <View style={styles.iconContainer}>
          <Ionicons name="wallet-outline" size={32} color="#007AFF" />
        </View>
        <Text style={styles.stepTitle}>How do you want to receive your money?</Text>
        <Text style={styles.stepDescription}>
          Choose your preferred payment method. Both options are fast and secure.
        </Text>
      </View>

      <View style={styles.paymentMethodsContainer}>
        {/* Cash Send Option - Beautiful Card */}
        <TouchableOpacity
          style={[
            styles.modernPaymentCard,
            applicationData.paymentMethod === 'cash_send' && styles.modernPaymentCardSelected
          ]}
          onPress={() => setApplicationData(prev => ({ ...prev, paymentMethod: 'cash_send' }))}
        >
          <LinearGradient
            colors={applicationData.paymentMethod === 'cash_send' ? ['#4CAF50', '#45a049'] : ['#f8f9fa', '#f8f9fa']}
            style={styles.paymentCardGradient}
          >
            <View style={styles.paymentCardHeader}>
              <View style={[
                styles.paymentIconContainer,
                applicationData.paymentMethod === 'cash_send' && styles.paymentIconContainerSelected
              ]}>
                <Ionicons
                  name="storefront"
                  size={28}
                  color={applicationData.paymentMethod === 'cash_send' ? 'white' : '#4CAF50'}
                />
              </View>
              <View style={styles.instantBadge}>
                <Text style={styles.instantBadgeText}>INSTANT</Text>
              </View>
            </View>

            <Text style={[
              styles.modernPaymentTitle,
              applicationData.paymentMethod === 'cash_send' && styles.modernPaymentTitleSelected
            ]}>
              Cash Send
            </Text>

            <Text style={[
              styles.modernPaymentDescription,
              applicationData.paymentMethod === 'cash_send' && styles.modernPaymentDescriptionSelected
            ]}>
              Pick up cash at any Shoprite, Checkers, or Pick n Pay store
            </Text>

            <View style={styles.paymentFeatures}>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={applicationData.paymentMethod === 'cash_send' ? 'rgba(255,255,255,0.9)' : '#4CAF50'}
                />
                <Text style={[
                  styles.featureItemText,
                  applicationData.paymentMethod === 'cash_send' && styles.featureItemTextSelected
                ]}>
                  Available immediately
                </Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={applicationData.paymentMethod === 'cash_send' ? 'rgba(255,255,255,0.9)' : '#4CAF50'}
                />
                <Text style={[
                  styles.featureItemText,
                  applicationData.paymentMethod === 'cash_send' && styles.featureItemTextSelected
                ]}>
                  No bank account needed
                </Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={applicationData.paymentMethod === 'cash_send' ? 'rgba(255,255,255,0.9)' : '#4CAF50'}
                />
                <Text style={[
                  styles.featureItemText,
                  applicationData.paymentMethod === 'cash_send' && styles.featureItemTextSelected
                ]}>
                  Thousands of locations
                </Text>
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>

        {/* EFT Option - Beautiful Card */}
        <TouchableOpacity
          style={[
            styles.modernPaymentCard,
            applicationData.paymentMethod === 'eft' && styles.modernPaymentCardSelected
          ]}
          onPress={() => setApplicationData(prev => ({ ...prev, paymentMethod: 'eft' }))}
        >
          <LinearGradient
            colors={applicationData.paymentMethod === 'eft' ? ['#007AFF', '#0056CC'] : ['#f8f9fa', '#f8f9fa']}
            style={styles.paymentCardGradient}
          >
            <View style={styles.paymentCardHeader}>
              <View style={[
                styles.paymentIconContainer,
                applicationData.paymentMethod === 'eft' && styles.paymentIconContainerSelected
              ]}>
                <Ionicons
                  name="card"
                  size={28}
                  color={applicationData.paymentMethod === 'eft' ? 'white' : '#007AFF'}
                />
              </View>
              <View style={[styles.fastBadge, applicationData.paymentMethod === 'eft' && styles.fastBadgeSelected]}>
                <Text style={[styles.fastBadgeText, applicationData.paymentMethod === 'eft' && styles.fastBadgeTextSelected]}>FAST</Text>
              </View>
            </View>

            <Text style={[
              styles.modernPaymentTitle,
              applicationData.paymentMethod === 'eft' && styles.modernPaymentTitleSelected
            ]}>
              Bank Transfer
            </Text>

            <Text style={[
              styles.modernPaymentDescription,
              applicationData.paymentMethod === 'eft' && styles.modernPaymentDescriptionSelected
            ]}>
              Direct transfer to your bank account
            </Text>

            <View style={styles.paymentFeatures}>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={applicationData.paymentMethod === 'eft' ? 'rgba(255,255,255,0.9)' : '#007AFF'}
                />
                <Text style={[
                  styles.featureItemText,
                  applicationData.paymentMethod === 'eft' && styles.featureItemTextSelected
                ]}>
                  2-4 hour processing
                </Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={applicationData.paymentMethod === 'eft' ? 'rgba(255,255,255,0.9)' : '#007AFF'}
                />
                <Text style={[
                  styles.featureItemText,
                  applicationData.paymentMethod === 'eft' && styles.featureItemTextSelected
                ]}>
                  Secure & private
                </Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={applicationData.paymentMethod === 'eft' ? 'rgba(255,255,255,0.9)' : '#007AFF'}
                />
                <Text style={[
                  styles.featureItemText,
                  applicationData.paymentMethod === 'eft' && styles.featureItemTextSelected
                ]}>
                  Direct to your account
                </Text>
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {errors.paymentMethod && (
        <Text style={styles.errorText}>{errors.paymentMethod}</Text>
      )}
    </View>
  );

  // Step 3: Repayment Date and Summary - Beautiful Design
  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      {/* Header with Icon */}
      <View style={styles.stepHeader}>
        <View style={styles.iconContainer}>
          <Ionicons name="calendar-outline" size={32} color="#007AFF" />
        </View>
        <Text style={styles.stepTitle}>When will you repay?</Text>
        <Text style={styles.stepDescription}>
          Choose your repayment date. Maximum 30 days from today.
        </Text>
      </View>

      {/* Beautiful Date Selection */}
      <View style={styles.dateSelectionContainer}>
        <Text style={styles.dateSelectionLabel}>Select Repayment Date</Text>
        <View style={styles.quickDateGrid}>
          {[7, 14, 21, 30].map((days) => {
            const date = new Date();
            date.setDate(date.getDate() + days);
            const dateString = date.toISOString().split('T')[0];
            const isSelected = applicationData.repaymentDate === dateString;

            return (
              <TouchableOpacity
                key={days}
                style={[
                  styles.modernDateCard,
                  isSelected && styles.modernDateCardSelected
                ]}
                onPress={() => setApplicationData(prev => ({ ...prev, repaymentDate: dateString }))}
              >
                <LinearGradient
                  colors={isSelected ? ['#007AFF', '#0056CC'] : ['#ffffff', '#ffffff']}
                  style={styles.dateCardGradient}
                >
                  <Text style={[
                    styles.dateCardDays,
                    isSelected && styles.dateCardDaysSelected
                  ]}>
                    {days}
                  </Text>
                  <Text style={[
                    styles.dateCardDaysLabel,
                    isSelected && styles.dateCardDaysLabelSelected
                  ]}>
                    days
                  </Text>
                  <Text style={[
                    styles.dateCardDate,
                    isSelected && styles.dateCardDateSelected
                  ]}>
                    {date.toLocaleDateString('en-ZA', {
                      month: 'short',
                      day: 'numeric'
                    })}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            );
          })}
        </View>

        {errors.repaymentDate && (
          <Text style={styles.errorText}>{errors.repaymentDate}</Text>
        )}
      </View>

      {/* Beautiful Summary Card */}
      {calculatedLoan && (
        <View style={styles.finalSummaryContainer}>
          <Text style={styles.finalSummaryTitle}>Loan Agreement Summary</Text>

          {/* Main Summary Card */}
          <LinearGradient
            colors={['#ffffff', '#f8f9fa']}
            style={styles.modernSummaryCard}
          >
            {/* Amount Section */}
            <View style={styles.summaryAmountSection}>
              <View style={styles.summaryAmountRow}>
                <Text style={styles.summaryAmountLabel}>You receive</Text>
                <Text style={styles.summaryAmountValue}>{formatCurrency(calculatedLoan.principal)}</Text>
              </View>
              <View style={styles.summaryBreakdown}>
                <View style={styles.breakdownItem}>
                  <Text style={styles.breakdownLabel}>Interest ({loanSettings?.interestRate}%)</Text>
                  <Text style={styles.breakdownValue}>+{formatCurrency(calculatedLoan.interestAmount)}</Text>
                </View>
                <View style={styles.breakdownItem}>
                  <Text style={styles.breakdownLabel}>Processing fee</Text>
                  <Text style={styles.breakdownValue}>+{formatCurrency(calculatedLoan.processingFee)}</Text>
                </View>
              </View>
              <View style={styles.summaryTotalRow}>
                <Text style={styles.summaryTotalLabel}>You repay</Text>
                <Text style={styles.summaryTotalValue}>{formatCurrency(calculatedLoan.totalAmount)}</Text>
              </View>
            </View>

            {/* Details Section */}
            <View style={styles.summaryDetailsSection}>
              <View style={styles.summaryDetailRow}>
                <View style={styles.summaryDetailItem}>
                  <Ionicons name="wallet-outline" size={20} color="#007AFF" />
                  <View style={styles.summaryDetailText}>
                    <Text style={styles.summaryDetailLabel}>Payment Method</Text>
                    <Text style={styles.summaryDetailValue}>
                      {applicationData.paymentMethod === 'cash_send' ? 'Cash Send' : 'Bank Transfer'}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.summaryDetailRow}>
                <View style={styles.summaryDetailItem}>
                  <Ionicons name="calendar-outline" size={20} color="#007AFF" />
                  <View style={styles.summaryDetailText}>
                    <Text style={styles.summaryDetailLabel}>Repayment Date</Text>
                    <Text style={styles.summaryDetailValue}>
                      {applicationData.repaymentDate ?
                        new Date(applicationData.repaymentDate).toLocaleDateString('en-ZA', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        }) : 'Not selected'
                      }
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.summaryDetailRow}>
                <View style={styles.summaryDetailItem}>
                  <Ionicons name="time-outline" size={20} color="#007AFF" />
                  <View style={styles.summaryDetailText}>
                    <Text style={styles.summaryDetailLabel}>Loan Term</Text>
                    <Text style={styles.summaryDetailValue}>{applicationData.termDays} days</Text>
                  </View>
                </View>
              </View>
            </View>
          </LinearGradient>

          {/* Agreement Notice */}
          <View style={styles.agreementNotice}>
            <Ionicons name="information-circle-outline" size={20} color="#666" />
            <Text style={styles.agreementText}>
              By submitting this application, you agree to our terms and conditions.
              You will receive an SMS confirmation once approved.
            </Text>
          </View>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Modern Header with Gradient */}
      <LinearGradient
        colors={['#007AFF', '#0056CC']}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backIconButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>

          <View style={styles.headerTextContainer}>
            <Text style={styles.modernTitle}>Quick Loan</Text>
            <Text style={styles.modernSubtitle}>Step {step} of 3</Text>
          </View>

          <View style={styles.headerRight}>
            <Text style={styles.stepIndicator}>{step}/3</Text>
          </View>
        </View>

        {/* Modern Progress Bar */}
        <View style={styles.modernProgressContainer}>
          <View style={styles.modernProgressBar}>
            <Animated.View
              style={[
                styles.modernProgressFill,
                { width: `${(step / 3) * 100}%` }
              ]}
            />
          </View>
          <View style={styles.progressStepsContainer}>
            {[
              { step: 1, label: 'Amount', icon: 'cash-outline' },
              { step: 2, label: 'Payment', icon: 'wallet-outline' },
              { step: 3, label: 'Summary', icon: 'checkmark-circle-outline' }
            ].map((item) => (
              <View key={item.step} style={styles.modernProgressStep}>
                <View style={[
                  styles.progressStepCircle,
                  item.step <= step && styles.progressStepCircleActive,
                  item.step === step && styles.progressStepCircleCurrent
                ]}>
                  <Ionicons
                    name={item.icon as any}
                    size={16}
                    color={item.step <= step ? 'white' : '#ccc'}
                  />
                </View>
                <Text style={[
                  styles.modernProgressLabel,
                  item.step <= step && styles.modernProgressLabelActive
                ]}>
                  {item.label}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </LinearGradient>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Step Content */}
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
        </ScrollView>

        {/* Modern Navigation Buttons */}
        <View style={styles.modernButtonContainer}>
          {step > 1 && (
            <TouchableOpacity
              style={styles.modernBackButton}
              onPress={handlePrevious}
            >
              <Ionicons name="chevron-back" size={20} color="#007AFF" />
              <Text style={styles.modernBackButtonText}>Back</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.modernNextButton,
              step === 1 && { flex: 1 },
              loading && styles.modernNextButtonDisabled
            ]}
            onPress={step < 3 ? handleNext : handleSubmit}
            disabled={loading}
          >
            <LinearGradient
              colors={loading ? ['#ccc', '#999'] : ['#4CAF50', '#45a049']}
              style={styles.modernNextButtonGradient}
            >
              {loading ? (
                <Text style={styles.modernNextButtonText}>Submitting...</Text>
              ) : (
                <>
                  <Text style={styles.modernNextButtonText}>
                    {step < 3 ? 'Continue' : 'Submit Application'}
                  </Text>
                  <Ionicons
                    name={step < 3 ? "chevron-forward" : "checkmark"}
                    size={20}
                    color="white"
                  />
                </>
              )}
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },

  // Modern Header Styles
  modernHeader: {
    paddingTop: 10,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  backIconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTextContainer: {
    flex: 1,
    alignItems: 'center',
  },
  modernTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  modernSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
  },
  stepIndicator: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },

  // Modern Progress Bar
  modernProgressContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  modernProgressBar: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    marginBottom: 15,
  },
  modernProgressFill: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 2,
  },
  progressStepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modernProgressStep: {
    alignItems: 'center',
    flex: 1,
  },
  progressStepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  progressStepCircleActive: {
    backgroundColor: 'white',
  },
  progressStepCircleCurrent: {
    backgroundColor: '#4CAF50',
  },
  modernProgressLabel: {
    fontSize: 11,
    color: 'rgba(255,255,255,0.7)',
    fontWeight: '500',
  },
  modernProgressLabelActive: {
    color: 'white',
    fontWeight: '600',
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },

  // Step Container Styles
  stepContainer: {
    padding: 20,
  },
  stepHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 30,
  },
  stepDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 10,
  },
  errorText: {
    fontSize: 14,
    color: '#FF3B30',
    marginTop: 8,
    textAlign: 'center',
  },
  // Amount Input Styles
  amountInputContainer: {
    marginBottom: 30,
  },
  amountLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
    textAlign: 'center',
  },
  amountInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 16,
    paddingHorizontal: 20,
    paddingVertical: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  currencySymbol: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007AFF',
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1a1a1a',
    padding: 0,
  },

  // Quick Amount Buttons
  quickAmountContainer: {
    marginBottom: 30,
  },
  quickAmountLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
    textAlign: 'center',
  },
  quickAmountButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAmountButton: {
    width: (width - 60) / 2,
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  quickAmountButtonSelected: {
    borderColor: '#4CAF50',
    backgroundColor: '#f8fff8',
  },
  quickAmountButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  quickAmountButtonTextSelected: {
    color: '#4CAF50',
  },
  // Term Selection Styles
  termSelectionContainer: {
    marginBottom: 30,
  },
  termLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
    textAlign: 'center',
  },
  termCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  termCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 20,
    paddingHorizontal: 8,
    marginHorizontal: 4,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  termCardSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  termCardPopular: {
    borderColor: '#FF9500',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    backgroundColor: '#FF9500',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  popularBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: 'white',
  },
  termCardDays: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  termCardDaysSelected: {
    color: '#007AFF',
  },
  termCardLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  termCardLabelSelected: {
    color: '#007AFF',
  },

  // Preview Gradient Card Styles
  previewGradientCard: {
    borderRadius: 16,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  previewCardContent: {
    padding: 20,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 8,
  },
  previewAmounts: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  previewAmountItem: {
    alignItems: 'center',
    flex: 1,
  },
  previewAmountLabel: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 4,
  },
  previewAmountValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  previewAmountValueHighlight: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  previewDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  previewDetailItem: {
    alignItems: 'center',
  },
  previewDetailLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 4,
  },
  previewDetailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  // Modern Payment Method Styles
  paymentMethodsContainer: {
    marginBottom: 20,
  },
  modernPaymentCard: {
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  modernPaymentCardSelected: {
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  paymentCardGradient: {
    borderRadius: 16,
    padding: 20,
  },
  paymentCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  paymentIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  paymentIconContainerSelected: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  instantBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  instantBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: 'white',
  },
  fastBadge: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  fastBadgeSelected: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  fastBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: 'white',
  },
  fastBadgeTextSelected: {
    color: 'white',
  },
  modernPaymentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  modernPaymentTitleSelected: {
    color: 'white',
  },
  modernPaymentDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 16,
  },
  modernPaymentDescriptionSelected: {
    color: 'rgba(255,255,255,0.9)',
  },
  paymentFeatures: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureItemText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    fontWeight: '500',
  },
  featureItemTextSelected: {
    color: 'rgba(255,255,255,0.9)',
  },
  // Date Selection Styles
  dateSelectionContainer: {
    marginBottom: 30,
  },
  dateSelectionLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
    textAlign: 'center',
  },
  quickDateGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modernDateCard: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  modernDateCardSelected: {
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  dateCardGradient: {
    borderRadius: 12,
    paddingVertical: 20,
    paddingHorizontal: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  dateCardDays: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  dateCardDaysSelected: {
    color: 'white',
  },
  dateCardDaysLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  dateCardDaysLabelSelected: {
    color: 'rgba(255,255,255,0.8)',
  },
  dateCardDate: {
    fontSize: 12,
    color: '#999',
    fontWeight: '500',
  },
  dateCardDateSelected: {
    color: 'rgba(255,255,255,0.9)',
  },
  // Final Summary Styles
  finalSummaryContainer: {
    marginBottom: 20,
  },
  finalSummaryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 20,
    textAlign: 'center',
  },
  modernSummaryCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  summaryAmountSection: {
    marginBottom: 20,
  },
  summaryAmountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryAmountLabel: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  summaryAmountValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  summaryBreakdown: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  breakdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  breakdownLabel: {
    fontSize: 14,
    color: '#666',
  },
  breakdownValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  summaryTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 2,
    borderTopColor: '#007AFF',
  },
  summaryTotalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  summaryTotalValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  summaryDetailsSection: {
    marginTop: 20,
  },
  summaryDetailRow: {
    marginBottom: 16,
  },
  summaryDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summaryDetailText: {
    marginLeft: 12,
    flex: 1,
  },
  summaryDetailLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  summaryDetailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  agreementNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
  },
  agreementText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 18,
    marginLeft: 8,
    flex: 1,
  },
  // Modern Button Styles
  modernButtonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    alignItems: 'center',
  },
  modernBackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#f8f9fa',
    marginRight: 12,
  },
  modernBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginLeft: 4,
  },
  modernNextButton: {
    flex: 1,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  modernNextButtonDisabled: {
    shadowOpacity: 0.1,
    elevation: 2,
  },
  modernNextButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  modernNextButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginRight: 8,
  },
});

export default LoanApplicationScreen;
