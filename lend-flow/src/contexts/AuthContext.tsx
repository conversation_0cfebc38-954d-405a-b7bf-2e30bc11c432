import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import * as SecureStore from 'expo-secure-store';
import * as LocalAuthentication from 'expo-local-authentication';
import { User } from '../types';
import { userService } from '../services/supabase';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (phoneNumber: string, pin: string) => Promise<boolean>;
  loginWithBiometrics: () => Promise<boolean>;
  logout: () => Promise<void>;
  updateUser: (updates: Partial<User>) => Promise<boolean>;
  setupBiometrics: (phoneNumber: string, pin: string) => Promise<boolean>;
  isBiometricsEnabled: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBiometricsEnabled, setIsBiometricsEnabled] = useState(false);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const storedUser = await SecureStore.getItemAsync('user');
      const biometricsEnabled = await SecureStore.getItemAsync('biometricsEnabled');
      
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
      
      if (biometricsEnabled === 'true') {
        setIsBiometricsEnabled(true);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (phoneNumber: string, pin: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // In a real app, you would validate the PIN against a secure backend
      // For now, we'll simulate authentication
      let userData = await userService.getUserByPhone(phoneNumber);
      
      // If user doesn't exist, create them (no signup required)
      if (!userData) {
        userData = await userService.createUser({
          phoneNumber,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }
      
      if (userData) {
        setUser(userData);
        await SecureStore.setItemAsync('user', JSON.stringify(userData));
        await SecureStore.setItemAsync('userPin', pin);
        await SecureStore.setItemAsync('userPhone', phoneNumber);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithBiometrics = async (): Promise<boolean> => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      
      if (!hasHardware || !isEnrolled) {
        return false;
      }
      
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access Lend Flow',
        fallbackLabel: 'Use PIN instead',
      });
      
      if (result.success) {
        const storedPhone = await SecureStore.getItemAsync('userPhone');
        const storedPin = await SecureStore.getItemAsync('userPin');
        
        if (storedPhone && storedPin) {
          return await login(storedPhone, storedPin);
        }
      }
      
      return false;
    } catch (error) {
      console.error('Biometric login error:', error);
      return false;
    }
  };

  const setupBiometrics = async (phoneNumber: string, pin: string): Promise<boolean> => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      
      if (!hasHardware || !isEnrolled) {
        return false;
      }
      
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Enable biometric login for Lend Flow',
        fallbackLabel: 'Cancel',
      });
      
      if (result.success) {
        await SecureStore.setItemAsync('biometricsEnabled', 'true');
        await SecureStore.setItemAsync('userPhone', phoneNumber);
        await SecureStore.setItemAsync('userPin', pin);
        setIsBiometricsEnabled(true);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Biometrics setup error:', error);
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setUser(null);
      await SecureStore.deleteItemAsync('user');
      await SecureStore.deleteItemAsync('userPin');
      await SecureStore.deleteItemAsync('userPhone');
      await SecureStore.deleteItemAsync('biometricsEnabled');
      setIsBiometricsEnabled(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const updateUser = async (updates: Partial<User>): Promise<boolean> => {
    try {
      if (!user) return false;
      
      const updatedUser = await userService.updateUser(user.id, updates);
      
      if (updatedUser) {
        setUser(updatedUser);
        await SecureStore.setItemAsync('user', JSON.stringify(updatedUser));
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Update user error:', error);
      return false;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    loginWithBiometrics,
    logout,
    updateUser,
    setupBiometrics,
    isBiometricsEnabled,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
