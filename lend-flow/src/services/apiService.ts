import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { User, Loan, Payment, LoanApplication, Notification } from '../types';

// API Configuration
const API_BASE_URL = 'http://localhost:3000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await SecureStore.getItemAsync('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, clear storage
      await SecureStore.deleteItemAsync('authToken');
    }
    return Promise.reject(error);
  }
);

// Currency formatting utility for ZAR
export const formatCurrency = (amount: number | null | undefined): string => {
  if (amount === null || amount === undefined) return 'R 0.00';
  return `R ${parseFloat(amount.toString()).toLocaleString('en-ZA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

// Date formatting utility
export const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// API User Services
export const apiUserService = {
  async login(phoneNumber: string, pin: string): Promise<{ user: User; token: string } | null> {
    try {
      const response = await api.post('/auth/login', {
        phoneNumber,
        pin,
      });
      
      if (response.data.success) {
        const { user, token } = response.data.data;
        
        // Store token
        await SecureStore.setItemAsync('authToken', token);
        
        return { user, token };
      }
      
      return null;
    } catch (error) {
      console.error('API login error:', error);
      return null;
    }
  },

  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await api.get('/profile');
      if (response.data.success) {
        return response.data.data;
      }
      return null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  },

  async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    try {
      const response = await api.patch('/profile', updates);
      if (response.data.success) {
        return response.data.data;
      }
      return null;
    } catch (error) {
      console.error('Update user error:', error);
      return null;
    }
  }
};

// API Loan Services
export const apiLoanService = {
  async getUserLoans(userId: string): Promise<Loan[]> {
    try {
      const response = await api.get('/loans');
      if (response.data.success) {
        return response.data.data.map((loan: any) => ({
          ...loan,
          // Convert API response to match your Loan type
          applicationDate: loan.applicationDate || loan.createdAt?.split('T')[0],
          approvalDate: loan.approvalDate?.split('T')[0],
          disbursementDate: loan.disbursementDate?.split('T')[0],
          nextPaymentDate: loan.nextPaymentDate?.split('T')[0],
        }));
      }
      return [];
    } catch (error) {
      console.error('Get user loans error:', error);
      return [];
    }
  },

  async getLoanById(loanId: string): Promise<Loan | null> {
    try {
      const response = await api.get(`/loans/${loanId}`);
      if (response.data.success) {
        const loan = response.data.data;
        return {
          ...loan,
          applicationDate: loan.applicationDate || loan.createdAt?.split('T')[0],
          approvalDate: loan.approvalDate?.split('T')[0],
          disbursementDate: loan.disbursementDate?.split('T')[0],
          nextPaymentDate: loan.nextPaymentDate?.split('T')[0],
        };
      }
      return null;
    } catch (error) {
      console.error('Get loan by ID error:', error);
      return null;
    }
  },

  async getLoanSummary(): Promise<any> {
    try {
      const response = await api.get('/loans/summary');
      if (response.data.success) {
        return response.data.data;
      }
      return null;
    } catch (error) {
      console.error('Get loan summary error:', error);
      return null;
    }
  },

  async calculateLoan(amount: number, termMonths: number, interestRate?: number): Promise<any> {
    try {
      const response = await api.post('/loans/calculate', {
        amount,
        termMonths,
        interestRate: interestRate || 12,
      });
      if (response.data.success) {
        return response.data.data;
      }
      return null;
    } catch (error) {
      console.error('Calculate loan error:', error);
      return null;
    }
  },

  async createLoan(loanData: Partial<Loan>): Promise<Loan | null> {
    try {
      // For now, return mock data since loan creation endpoint isn't implemented
      return {
        id: `loan-${Date.now()}`,
        userId: loanData.userId || '',
        amount: loanData.amount || 0,
        interestRate: loanData.interestRate || 12,
        termMonths: loanData.termMonths || 12,
        monthlyPayment: loanData.monthlyPayment || 0,
        totalAmount: loanData.totalAmount || 0,
        status: 'pending',
        purpose: loanData.purpose || '',
        applicationDate: new Date().toISOString().split('T')[0],
        remainingBalance: loanData.amount || 0,
        paymentsCompleted: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...loanData,
      };
    } catch (error) {
      console.error('Create loan error:', error);
      return null;
    }
  },

  async updateLoan(loanId: string, updates: Partial<Loan>): Promise<Loan | null> {
    try {
      // For now, return mock data since loan update endpoint isn't implemented
      const loan = await this.getLoanById(loanId);
      if (loan) {
        return { ...loan, ...updates, updatedAt: new Date().toISOString() };
      }
      return null;
    } catch (error) {
      console.error('Update loan error:', error);
      return null;
    }
  }
};

// API Payment Services
export const apiPaymentService = {
  async getUserPayments(userId: string): Promise<Payment[]> {
    try {
      const response = await api.get('/payments');
      if (response.data.success) {
        return response.data.data;
      }
      return [];
    } catch (error) {
      console.error('Get user payments error:', error);
      return [];
    }
  },

  async getLoanPayments(loanId: string): Promise<Payment[]> {
    try {
      const response = await api.get(`/payments/loans/${loanId}`);
      if (response.data.success) {
        return response.data.data;
      }
      return [];
    } catch (error) {
      console.error('Get loan payments error:', error);
      return [];
    }
  },

  async getPaymentSummary(): Promise<any> {
    try {
      const response = await api.get('/payments/summary');
      if (response.data.success) {
        return response.data.data;
      }
      return null;
    } catch (error) {
      console.error('Get payment summary error:', error);
      return null;
    }
  },

  async createPayment(paymentData: Partial<Payment>): Promise<Payment | null> {
    try {
      // For now, return mock data since payment creation endpoint isn't implemented
      return {
        id: `payment-${Date.now()}`,
        loanId: paymentData.loanId || '',
        amount: paymentData.amount || 0,
        paymentDate: paymentData.paymentDate || new Date().toISOString().split('T')[0],
        dueDate: paymentData.dueDate || new Date().toISOString().split('T')[0],
        status: 'completed',
        paymentMethod: 'bank_transfer',
        createdAt: new Date().toISOString(),
        ...paymentData,
      };
    } catch (error) {
      console.error('Create payment error:', error);
      return null;
    }
  }
};

// API Notification Services
export const apiNotificationService = {
  async getUserNotifications(userId: string): Promise<Notification[]> {
    try {
      const response = await api.get('/profile/notifications');
      if (response.data.success) {
        return response.data.data;
      }
      return [];
    } catch (error) {
      console.error('Get notifications error:', error);
      // Return mock notifications for demo
      return [
        {
          id: 'demo-notif-1',
          userId,
          title: 'Payment Reminder',
          message: 'Your loan payment of R 2,355.93 is due on July 15th',
          type: 'payment_reminder',
          isRead: false,
          createdAt: '2024-07-10T10:00:00Z',
        },
        {
          id: 'demo-notif-2',
          userId,
          title: 'Loan Application Update',
          message: 'Your home improvement loan application is under review',
          type: 'general',
          isRead: false,
          createdAt: '2024-06-20T10:00:00Z',
        }
      ];
    }
  },

  async markNotificationAsRead(notificationId: string): Promise<boolean> {
    try {
      const response = await api.patch(`/profile/notifications/${notificationId}/read`);
      return response.data.success;
    } catch (error) {
      console.error('Mark notification as read error:', error);
      return true; // Mock success
    }
  },

  async createNotification(notificationData: Partial<Notification>): Promise<Notification | null> {
    try {
      // For now, return mock data
      return {
        id: `notification-${Date.now()}`,
        userId: notificationData.userId || '',
        title: notificationData.title || '',
        message: notificationData.message || '',
        type: notificationData.type || 'general',
        isRead: false,
        createdAt: new Date().toISOString(),
        ...notificationData,
      };
    } catch (error) {
      console.error('Create notification error:', error);
      return null;
    }
  }
};

// Health check
export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await axios.get(`${API_BASE_URL.replace('/api/v1', '')}/health`);
    return response.data.success;
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
};

export default api;
