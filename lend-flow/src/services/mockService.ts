import { User, Loan, Payment, LoanApplication, Notification } from '../types';

// Mock data for demo purposes
const DEMO_USER_PHONE = '0790775800';

// Mock User Services
export const mockUserService = {
  async getUserByPhone(phoneNumber: string): Promise<User | null> {
    if (phoneNumber === DEMO_USER_PHONE) {
      return {
        id: `user-${phoneNumber}`,
        phoneNumber,
        firstName: 'Demo',
        lastName: 'User',
        email: '<EMAIL>',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: new Date().toISOString(),
      };
    }
    return null;
  },

  async createUser(userData: Partial<User>): Promise<User | null> {
    return {
      id: `user-${userData.phoneNumber}`,
      phoneNumber: userData.phoneNumber || '',
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      createdAt: userData.createdAt || new Date().toISOString(),
      updatedAt: userData.updatedAt || new Date().toISOString(),
    };
  },

  async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    // Mock update - in real app this would update the database
    return {
      id: userId,
      phoneNumber: updates.phoneNumber || DEMO_USER_PHONE,
      firstName: updates.firstName || 'Demo',
      lastName: updates.lastName || 'User',
      email: updates.email || '<EMAIL>',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: new Date().toISOString(),
      ...updates,
    };
  }
};

// Mock Loan Services
export const mockLoanService = {
  async getUserLoans(userId: string): Promise<Loan[]> {
    if (userId.includes(DEMO_USER_PHONE)) {
      return [
        {
          id: 'demo-loan-1',
          userId,
          amount: 15000,
          interestRate: 12,
          termMonths: 24,
          monthlyPayment: 706.78,
          totalAmount: 16962.72,
          status: 'active',
          purpose: 'Business expansion',
          applicationDate: '2024-01-15',
          approvalDate: '2024-01-16',
          disbursementDate: '2024-01-17',
          nextPaymentDate: '2024-07-15',
          remainingBalance: 8500,
          paymentsCompleted: 12,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-06-15T10:00:00Z',
        },
        {
          id: 'demo-loan-2',
          userId,
          amount: 5000,
          interestRate: 15,
          termMonths: 12,
          monthlyPayment: 450.26,
          totalAmount: 5403.12,
          status: 'completed',
          purpose: 'Emergency fund',
          applicationDate: '2023-06-01',
          approvalDate: '2023-06-02',
          disbursementDate: '2023-06-03',
          nextPaymentDate: undefined,
          remainingBalance: 0,
          paymentsCompleted: 12,
          createdAt: '2023-06-01T10:00:00Z',
          updatedAt: '2024-06-01T10:00:00Z',
        },
        {
          id: 'demo-loan-3',
          userId,
          amount: 25000,
          interestRate: 10,
          termMonths: 36,
          monthlyPayment: 806.67,
          totalAmount: 29040.12,
          status: 'pending',
          purpose: 'Home improvement',
          applicationDate: '2024-06-20',
          approvalDate: undefined,
          disbursementDate: undefined,
          nextPaymentDate: undefined,
          remainingBalance: 25000,
          paymentsCompleted: 0,
          createdAt: '2024-06-20T10:00:00Z',
          updatedAt: '2024-06-20T10:00:00Z',
        }
      ];
    }
    return [];
  },

  async getLoanById(loanId: string): Promise<Loan | null> {
    const loans = await this.getUserLoans('demo-user');
    return loans.find(loan => loan.id === loanId) || null;
  },

  async createLoan(loanData: Partial<Loan>): Promise<Loan | null> {
    return {
      id: `loan-${Date.now()}`,
      userId: loanData.userId || '',
      amount: loanData.amount || 0,
      interestRate: loanData.interestRate || 12,
      termMonths: loanData.termMonths || 12,
      monthlyPayment: loanData.monthlyPayment || 0,
      totalAmount: loanData.totalAmount || 0,
      status: 'pending',
      purpose: loanData.purpose || '',
      applicationDate: new Date().toISOString().split('T')[0],
      remainingBalance: loanData.amount || 0,
      paymentsCompleted: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...loanData,
    };
  },

  async updateLoan(loanId: string, updates: Partial<Loan>): Promise<Loan | null> {
    // Mock update
    const loan = await this.getLoanById(loanId);
    if (loan) {
      return { ...loan, ...updates, updatedAt: new Date().toISOString() };
    }
    return null;
  }
};

// Mock Payment Services
export const mockPaymentService = {
  async getLoanPayments(loanId: string): Promise<Payment[]> {
    if (loanId === 'demo-loan-1') {
      return [
        {
          id: 'demo-payment-1',
          loanId,
          amount: 706.78,
          paymentDate: '2024-06-15',
          dueDate: '2024-06-15',
          status: 'completed',
          paymentMethod: 'bank_transfer',
          createdAt: '2024-06-15T10:00:00Z',
        },
        {
          id: 'demo-payment-2',
          loanId,
          amount: 706.78,
          paymentDate: '2024-07-15',
          dueDate: '2024-07-15',
          status: 'pending',
          paymentMethod: 'bank_transfer',
          createdAt: '2024-07-15T10:00:00Z',
        }
      ];
    }
    return [];
  },

  async createPayment(paymentData: Partial<Payment>): Promise<Payment | null> {
    return {
      id: `payment-${Date.now()}`,
      loanId: paymentData.loanId || '',
      amount: paymentData.amount || 0,
      paymentDate: paymentData.paymentDate || new Date().toISOString().split('T')[0],
      dueDate: paymentData.dueDate || new Date().toISOString().split('T')[0],
      status: 'completed',
      paymentMethod: 'bank_transfer',
      createdAt: new Date().toISOString(),
      ...paymentData,
    };
  },

  async getUserPayments(userId: string): Promise<Payment[]> {
    if (userId.includes(DEMO_USER_PHONE)) {
      return [
        {
          id: 'demo-payment-1',
          loanId: 'demo-loan-1',
          amount: 706.78,
          paymentDate: '2024-06-15',
          dueDate: '2024-06-15',
          status: 'completed',
          paymentMethod: 'bank_transfer',
          createdAt: '2024-06-15T10:00:00Z',
        },
        {
          id: 'demo-payment-2',
          loanId: 'demo-loan-1',
          amount: 706.78,
          paymentDate: '2024-07-15',
          dueDate: '2024-07-15',
          status: 'pending',
          paymentMethod: 'bank_transfer',
          createdAt: '2024-07-15T10:00:00Z',
        }
      ];
    }
    return [];
  }
};

// Mock Notification Services
export const mockNotificationService = {
  async getUserNotifications(userId: string): Promise<Notification[]> {
    if (userId.includes(DEMO_USER_PHONE)) {
      return [
        {
          id: 'demo-notif-1',
          userId,
          title: 'Payment Reminder',
          message: 'Your loan payment of $706.78 is due on July 15th',
          type: 'payment_reminder',
          isRead: false,
          createdAt: '2024-07-10T10:00:00Z',
        },
        {
          id: 'demo-notif-2',
          userId,
          title: 'Loan Application Update',
          message: 'Your home improvement loan application is under review',
          type: 'general',
          isRead: false,
          createdAt: '2024-06-20T10:00:00Z',
        }
      ];
    }
    return [];
  },

  async markNotificationAsRead(notificationId: string): Promise<boolean> {
    // Mock implementation
    return true;
  },

  async createNotification(notificationData: Partial<Notification>): Promise<Notification | null> {
    return {
      id: `notification-${Date.now()}`,
      userId: notificationData.userId || '',
      title: notificationData.title || '',
      message: notificationData.message || '',
      type: notificationData.type || 'general',
      isRead: false,
      createdAt: new Date().toISOString(),
      ...notificationData,
    };
  }
};
