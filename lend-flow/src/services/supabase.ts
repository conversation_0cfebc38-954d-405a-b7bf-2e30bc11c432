import { createClient } from '@supabase/supabase-js';
import { User, Loan, Payment, LoanApplication, Notification } from '../types';

// Replace with your actual Supabase URL and anon key
const SUPABASE_URL = 'https://your-project.supabase.co';
const SUPABASE_ANON_KEY = 'your-anon-key';

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// User Services
export const userService = {
  async getUserByPhone(phoneNumber: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('phone_number', phoneNumber)
      .single();
    
    if (error) {
      console.error('Error fetching user:', error);
      return null;
    }
    
    return data;
  },

  async createUser(userData: Partial<User>): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .insert([userData])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating user:', error);
      return null;
    }
    
    return data;
  },

  async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating user:', error);
      return null;
    }
    
    return data;
  }
};

// Loan Services
export const loanService = {
  async getUserLoans(userId: string): Promise<Loan[]> {
    const { data, error } = await supabase
      .from('loans')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching loans:', error);
      return [];
    }
    
    return data || [];
  },

  async getLoanById(loanId: string): Promise<Loan | null> {
    const { data, error } = await supabase
      .from('loans')
      .select('*')
      .eq('id', loanId)
      .single();
    
    if (error) {
      console.error('Error fetching loan:', error);
      return null;
    }
    
    return data;
  },

  async createLoan(loanData: Partial<Loan>): Promise<Loan | null> {
    const { data, error } = await supabase
      .from('loans')
      .insert([loanData])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating loan:', error);
      return null;
    }
    
    return data;
  },

  async updateLoan(loanId: string, updates: Partial<Loan>): Promise<Loan | null> {
    const { data, error } = await supabase
      .from('loans')
      .update(updates)
      .eq('id', loanId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating loan:', error);
      return null;
    }
    
    return data;
  }
};

// Payment Services
export const paymentService = {
  async getLoanPayments(loanId: string): Promise<Payment[]> {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('loan_id', loanId)
      .order('payment_date', { ascending: false });
    
    if (error) {
      console.error('Error fetching payments:', error);
      return [];
    }
    
    return data || [];
  },

  async createPayment(paymentData: Partial<Payment>): Promise<Payment | null> {
    const { data, error } = await supabase
      .from('payments')
      .insert([paymentData])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating payment:', error);
      return null;
    }
    
    return data;
  },

  async getUserPayments(userId: string): Promise<Payment[]> {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        loans!inner(user_id)
      `)
      .eq('loans.user_id', userId)
      .order('payment_date', { ascending: false });
    
    if (error) {
      console.error('Error fetching user payments:', error);
      return [];
    }
    
    return data || [];
  }
};

// Application Services
export const applicationService = {
  async createApplication(appData: Partial<LoanApplication>): Promise<LoanApplication | null> {
    const { data, error } = await supabase
      .from('loan_applications')
      .insert([appData])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating application:', error);
      return null;
    }
    
    return data;
  },

  async getUserApplications(userId: string): Promise<LoanApplication[]> {
    const { data, error } = await supabase
      .from('loan_applications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching applications:', error);
      return [];
    }
    
    return data || [];
  }
};

// Notification Services
export const notificationService = {
  async getUserNotifications(userId: string): Promise<Notification[]> {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
    
    return data || [];
  },

  async markNotificationAsRead(notificationId: string): Promise<boolean> {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);
    
    if (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
    
    return true;
  },

  async createNotification(notificationData: Partial<Notification>): Promise<Notification | null> {
    const { data, error } = await supabase
      .from('notifications')
      .insert([notificationData])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating notification:', error);
      return null;
    }
    
    return data;
  }
};
