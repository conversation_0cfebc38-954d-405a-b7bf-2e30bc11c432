import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';

// Import screens (we'll create these next)
import LoginScreen from '../screens/LoginScreen';
import DashboardScreen from '../screens/DashboardScreen';
import LoansScreen from '../screens/LoansScreen';
import LoanCalculatorScreen from '../screens/LoanCalculatorScreen';
import ProfileScreen from '../screens/ProfileScreen';
import LoanApplicationScreen from '../screens/LoanApplicationScreen';
import LoanDetailsScreen from '../screens/LoanDetailsScreen';
import PaymentHistoryScreen from '../screens/PaymentHistoryScreen';
import NotificationsScreen from '../screens/NotificationsScreen';

import { RootStackParamList, MainTabParamList } from '../types';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Dashboard') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Loans') {
            iconName = focused ? 'card' : 'card-outline';
          } else if (route.name === 'Calculator') {
            iconName = focused ? 'calculator' : 'calculator-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ tabBarLabel: 'Home' }}
      />
      <Tab.Screen 
        name="Loans" 
        component={LoansScreen}
        options={{ tabBarLabel: 'My Loans' }}
      />
      <Tab.Screen 
        name="Calculator" 
        component={LoanCalculatorScreen}
        options={{ tabBarLabel: 'Calculator' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ tabBarLabel: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    // You can replace this with a proper loading screen
    return null;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: '#007AFF',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        {!isAuthenticated ? (
          <Stack.Screen 
            name="Auth" 
            component={LoginScreen}
            options={{ headerShown: false }}
          />
        ) : (
          <>
            <Stack.Screen 
              name="Main" 
              component={MainTabNavigator}
              options={{ headerShown: false }}
            />
            <Stack.Screen 
              name="LoanCalculator" 
              component={LoanCalculatorScreen}
              options={{ 
                title: 'Loan Calculator',
                presentation: 'modal'
              }}
            />
            <Stack.Screen 
              name="LoanApplication" 
              component={LoanApplicationScreen}
              options={{ 
                title: 'Apply for Loan',
                presentation: 'modal'
              }}
            />
            <Stack.Screen 
              name="LoanDetails" 
              component={LoanDetailsScreen}
              options={{ title: 'Loan Details' }}
            />
            <Stack.Screen 
              name="PaymentHistory" 
              component={PaymentHistoryScreen}
              options={{ title: 'Payment History' }}
            />
            <Stack.Screen 
              name="Notifications" 
              component={NotificationsScreen}
              options={{ title: 'Notifications' }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
