// User Types
export interface User {
  id: string;
  phoneNumber: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  address?: string;
  bankingInfo?: BankingInfo;
  createdAt: string;
  updatedAt: string;
}

export interface BankingInfo {
  accountNumber: string;
  bankName: string;
  accountType: 'savings' | 'checking';
  routingNumber?: string;
}

// Loan Types
export interface Loan {
  id: string;
  userId: string;
  amount: number;
  interestRate: number;
  termMonths: number;
  monthlyPayment: number;
  totalAmount: number;
  status: LoanStatus;
  purpose: string;
  applicationDate: string;
  approvalDate?: string;
  disbursementDate?: string;
  nextPaymentDate?: string;
  remainingBalance: number;
  paymentsCompleted: number;
  createdAt: string;
  updatedAt: string;
}

export type LoanStatus = 
  | 'pending' 
  | 'approved' 
  | 'rejected' 
  | 'active' 
  | 'completed' 
  | 'defaulted';

// Payment Types
export interface Payment {
  id: string;
  loanId: string;
  amount: number;
  paymentDate: string;
  dueDate: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  transactionId?: string;
  lateFee?: number;
  createdAt: string;
}

export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'late';
export type PaymentMethod = 'bank_transfer' | 'card' | 'mobile_money';

// Loan Calculator Types
export interface LoanCalculation {
  principal: number;
  interestRate: number;
  termMonths: number;
  monthlyPayment: number;
  totalInterest: number;
  totalAmount: number;
  paymentSchedule: PaymentScheduleItem[];
}

export interface PaymentScheduleItem {
  paymentNumber: number;
  paymentDate: string;
  paymentAmount: number;
  principalAmount: number;
  interestAmount: number;
  remainingBalance: number;
}

// Application Types
export interface LoanApplication {
  id: string;
  userId: string;
  requestedAmount: number;
  purpose: string;
  employmentInfo: EmploymentInfo;
  monthlyIncome: number;
  existingDebts: number;
  status: ApplicationStatus;
  documents: Document[];
  createdAt: string;
  updatedAt: string;
}

export interface EmploymentInfo {
  employer: string;
  position: string;
  employmentType: 'full_time' | 'part_time' | 'self_employed' | 'unemployed';
  monthlyIncome: number;
  yearsEmployed: number;
}

export interface Document {
  id: string;
  type: DocumentType;
  url: string;
  fileName: string;
  uploadedAt: string;
}

export type DocumentType = 
  | 'id_document' 
  | 'proof_of_income' 
  | 'bank_statement' 
  | 'employment_letter';

export type ApplicationStatus = 
  | 'draft' 
  | 'submitted' 
  | 'under_review' 
  | 'approved' 
  | 'rejected';

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  actionUrl?: string;
  createdAt: string;
}

export type NotificationType = 
  | 'payment_reminder' 
  | 'payment_overdue' 
  | 'loan_approved' 
  | 'loan_rejected' 
  | 'payment_received' 
  | 'general';

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  LoanCalculator: undefined;
  LoanApplication: { calculatedLoan?: LoanCalculation };
  LoanDetails: { loanId: string };
  PaymentHistory: { loanId: string };
  Profile: undefined;
  Notifications: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Loans: undefined;
  Calculator: undefined;
  Profile: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form Types
export interface LoginForm {
  phoneNumber: string;
  pin: string;
}

export interface LoanApplicationForm {
  amount: number;
  purpose: string;
  termMonths: number;
  employmentInfo: EmploymentInfo;
  monthlyIncome: number;
  existingDebts: number;
}

// Dashboard Types
export interface DashboardData {
  activeLoans: Loan[];
  totalOutstanding: number;
  nextPaymentDue: Payment | null;
  recentPayments: Payment[];
  creditScore?: number;
}
