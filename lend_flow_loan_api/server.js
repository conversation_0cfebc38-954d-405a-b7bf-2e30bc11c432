const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data for demo (amounts in ZAR)
const mockLoans = [
  {
    id: 'demo-loan-1',
    userId: 'user-**********',
    amount: 50000, // R50,000
    interestRate: 12,
    termMonths: 24,
    monthlyPayment: 2355.93, // R2,355.93
    totalAmount: 56542.32, // R56,542.32
    status: 'active',
    purpose: 'Business expansion',
    applicationDate: '2024-01-15',
    approvalDate: '2024-01-16',
    disbursementDate: '2024-01-17',
    nextPaymentDate: '2024-07-15',
    remainingBalance: 28333.33, // R28,333.33
    paymentsCompleted: 12,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-06-15T10:00:00Z',
  },
  {
    id: 'demo-loan-2',
    userId: 'user-**********',
    amount: 15000, // R15,000
    interestRate: 15,
    termMonths: 12,
    monthlyPayment: 1350.78, // R1,350.78
    totalAmount: 16209.36, // R16,209.36
    status: 'completed',
    purpose: 'Emergency fund',
    applicationDate: '2023-06-01',
    approvalDate: '2023-06-02',
    disbursementDate: '2023-06-03',
    nextPaymentDate: undefined,
    remainingBalance: 0,
    paymentsCompleted: 12,
    createdAt: '2023-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z',
  },
  {
    id: 'demo-loan-3',
    userId: 'user-**********',
    amount: 75000, // R75,000
    interestRate: 10,
    termMonths: 36,
    monthlyPayment: 2420.01, // R2,420.01
    totalAmount: 87120.36, // R87,120.36
    status: 'pending',
    purpose: 'Home improvement',
    applicationDate: '2024-06-20',
    approvalDate: undefined,
    disbursementDate: undefined,
    nextPaymentDate: undefined,
    remainingBalance: 75000,
    paymentsCompleted: 0,
    createdAt: '2024-06-20T10:00:00Z',
    updatedAt: '2024-06-20T10:00:00Z',
  }
];

const mockPayments = [
  {
    id: 'demo-payment-1',
    loanId: 'demo-loan-1',
    amount: 2355.93, // R2,355.93
    paymentDate: '2024-06-15',
    dueDate: '2024-06-15',
    status: 'completed',
    paymentMethod: 'bank_transfer',
    createdAt: '2024-06-15T10:00:00Z',
  },
  {
    id: 'demo-payment-2',
    loanId: 'demo-loan-1',
    amount: 2355.93, // R2,355.93
    paymentDate: '2024-07-15',
    dueDate: '2024-07-15',
    status: 'pending',
    paymentMethod: 'bank_transfer',
    createdAt: '2024-07-15T10:00:00Z',
  }
];

const mockUser = {
  id: 'user-**********',
  phoneNumber: '**********',
  firstName: 'Demo',
  lastName: 'User',
  email: '<EMAIL>',
  address: '123 Demo Street, Demo City',
  creditScore: 720,
  isActive: true,
  isVerified: true,
  bankingInfo: {
    accountNumber: '**********',
    bankName: 'Demo Bank',
    accountType: 'savings',
    accountHolderName: 'Demo User'
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: new Date().toISOString()
};

const mockSettings = {
  id: 'default-settings',
  minLoanAmount: 5000, // R5,000 minimum
  maxLoanAmount: 500000, // R500,000 maximum
  minTermMonths: 6,
  maxTermMonths: 60,
  baseInterestRate: 12,
  maxInterestRate: 30,
  lateFeePercentage: 5,
  lateFeeFixedAmount: 150, // R150
  gracePeriodDays: 7,
  maxDebtToIncomeRatio: 0.4,
  minCreditScore: 300,
  processingFeePercentage: 2,
  processingFeeFixedAmount: 300, // R300
  isActive: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: new Date().toISOString()
};

// Simple authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token required'
    });
  }

  if (token.startsWith('demo-')) {
    req.user = { id: 'user-**********', phoneNumber: '**********' };
    next();
  } else {
    res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  }
};

// Routes
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Lend Flow API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Auth endpoints
app.post('/api/v1/auth/login', (req, res) => {
  const { phoneNumber, pin } = req.body;

  if (phoneNumber === '**********' && pin === '9999') {
    const token = 'demo-token-' + Date.now();
    
    res.json({
      success: true,
      data: {
        user: mockUser,
        token
      },
      message: 'Login successful'
    });
  } else {
    res.status(401).json({
      success: false,
      error: 'Invalid credentials'
    });
  }
});

// Loan endpoints
app.get('/api/v1/loans', authenticateToken, (req, res) => {
  const { status } = req.query;
  let loans = mockLoans;
  
  if (status) {
    loans = loans.filter(loan => loan.status === status);
  }
  
  res.json({
    success: true,
    data: loans
  });
});

app.get('/api/v1/loans/summary', authenticateToken, (req, res) => {
  const activeLoans = mockLoans.filter(loan => loan.status === 'active');
  
  const summary = {
    totalLoans: mockLoans.length,
    activeLoans: activeLoans.length,
    completedLoans: mockLoans.filter(loan => loan.status === 'completed').length,
    totalBorrowed: mockLoans.reduce((sum, loan) => sum + loan.amount, 0),
    totalOutstanding: activeLoans.reduce((sum, loan) => sum + loan.remainingBalance, 0),
    totalPaid: mockLoans.reduce((sum, loan) => sum + (loan.amount - loan.remainingBalance), 0),
    nextPaymentDue: activeLoans
      .filter(loan => loan.nextPaymentDate)
      .sort((a, b) => new Date(a.nextPaymentDate) - new Date(b.nextPaymentDate))[0]
  };

  res.json({
    success: true,
    data: summary
  });
});

app.post('/api/v1/loans/calculate', (req, res) => {
  const { amount, termMonths, interestRate = 12 } = req.body;

  if (!amount || !termMonths) {
    return res.status(400).json({
      success: false,
      error: 'Amount and term are required'
    });
  }

  const monthlyRate = interestRate / 100 / 12;
  let monthlyPayment;

  if (monthlyRate === 0) {
    monthlyPayment = amount / termMonths;
  } else {
    const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
    const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
    monthlyPayment = amount * (numerator / denominator);
  }

  const totalAmount = monthlyPayment * termMonths;
  const totalInterest = totalAmount - amount;

  res.json({
    success: true,
    data: {
      principal: amount,
      interestRate,
      termMonths,
      monthlyPayment: Math.round(monthlyPayment * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      totalAmount: Math.round(totalAmount * 100) / 100
    }
  });
});

// Payment endpoints
app.get('/api/v1/payments', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: mockPayments
  });
});

app.get('/api/v1/payments/summary', authenticateToken, (req, res) => {
  const summary = {
    totalPayments: mockPayments.length,
    totalAmountPaid: mockPayments
      .filter(p => p.status === 'completed')
      .reduce((sum, payment) => sum + payment.amount, 0),
    pendingPayments: mockPayments.filter(p => p.status === 'pending').length,
    recentPayments: mockPayments
      .filter(p => p.status === 'completed')
      .sort((a, b) => new Date(b.paymentDate) - new Date(a.paymentDate))
      .slice(0, 5)
  };

  res.json({
    success: true,
    data: summary
  });
});

// Profile endpoints
app.get('/api/v1/profile', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: mockUser
  });
});

// Loan settings endpoints
app.get('/api/v1/loan-settings', (req, res) => {
  res.json({
    success: true,
    data: mockSettings
  });
});

// API documentation
app.get('/api/v1/docs', (req, res) => {
  res.json({
    success: true,
    message: 'Lend Flow API Documentation',
    version: '1.0.0',
    endpoints: {
      'POST /api/v1/auth/login': 'Login with phone number and PIN',
      'GET /api/v1/loans': 'Get user loans',
      'GET /api/v1/loans/summary': 'Get loan summary',
      'POST /api/v1/loans/calculate': 'Calculate loan details',
      'GET /api/v1/payments': 'Get user payments',
      'GET /api/v1/payments/summary': 'Get payment summary',
      'GET /api/v1/profile': 'Get user profile',
      'GET /api/v1/loan-settings': 'Get loan settings'
    },
    demoCredentials: {
      phoneNumber: '**********',
      pin: '9999'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `The requested endpoint ${req.method} ${req.originalUrl} was not found.`
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`
🚀 Lend Flow API Server Started Successfully!

📍 Server Details:
   • Port: ${PORT}
   • Environment: development
   • Base URL: http://localhost:${PORT}

📚 API Documentation:
   • Docs: http://localhost:${PORT}/api/v1/docs
   • Health: http://localhost:${PORT}/health

🔐 Demo Credentials:
   • Phone: **********
   • PIN: 9999

🛠️  Available Endpoints:
   • POST /api/v1/auth/login - Login
   • GET /api/v1/loans - Get loans
   • GET /api/v1/loans/summary - Loan summary
   • POST /api/v1/loans/calculate - Calculate loan
   • GET /api/v1/payments - Get payments
   • GET /api/v1/payments/summary - Payment summary
   • GET /api/v1/profile - Get profile
   • GET /api/v1/loan-settings - Get settings

⚡ Ready to handle requests!
  `);
});
