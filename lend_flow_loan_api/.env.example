# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# Database Configuration (for future use)
DATABASE_URL=postgresql://username:password@localhost:5432/lendflow

# API Configuration
API_VERSION=v1
API_PREFIX=/api

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=*

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Notification Configuration
ENABLE_SMS=false
ENABLE_EMAIL=false

# SMS Configuration (for future integration)
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.sms-provider.com

# Email Configuration (for future integration)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# Payment Gateway Configuration (for future integration)
PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
PAYMENT_GATEWAY_KEY=your-payment-gateway-key
PAYMENT_GATEWAY_SECRET=your-payment-gateway-secret

# Credit Check Service Configuration (for future integration)
CREDIT_CHECK_API_URL=https://api.credit-bureau.com
CREDIT_CHECK_API_KEY=your-credit-check-api-key

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security Configuration
BCRYPT_ROUNDS=12

# Business Rules Configuration
DEFAULT_INTEREST_RATE=12
MAX_LOAN_AMOUNT=100000
MIN_LOAN_AMOUNT=1000
MAX_LOAN_TERM_MONTHS=60
MIN_LOAN_TERM_MONTHS=6

# Feature Flags
ENABLE_CREDIT_CHECK=false
ENABLE_RISK_ASSESSMENT=true
ENABLE_AUTO_APPROVAL=true
ENABLE_SWAGGER=true
ENABLE_MORGAN_LOGGING=true
