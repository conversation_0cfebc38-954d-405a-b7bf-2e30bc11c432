# Lend Flow Loan Management API

A comprehensive RESTful API for loan management, built with Node.js, Express, and TypeScript. This API provides complete loan lifecycle management including applications, approvals, payments, and user profile management.

## 🚀 Features

### 🔐 Authentication & Security
- Phone number-based authentication with PIN
- JWT token-based authorization
- OTP verification for phone numbers
- PIN reset functionality
- Rate limiting and security headers

### 💰 Loan Management
- Loan application creation and processing
- Automated risk assessment and approval
- Loan calculation with payment schedules
- Loan status tracking and updates
- Loan history and summary

### 💳 Payment Management
- Payment processing and tracking
- Payment schedule generation
- Early payoff calculations
- Payment history and statistics
- Automated payment reminders

### 👤 Profile Management
- User profile management
- Financial summary and statistics
- Notification preferences
- Account verification status
- Banking information management

### ⚙️ Loan Settings & Configuration
- Configurable loan parameters
- Eligibility criteria checking
- Interest rate calculation
- Risk-based pricing

### 🔔 Notification System
- Payment reminders
- Loan status updates
- System notifications
- Notification preferences

## 🛠️ Technology Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Language**: TypeScript
- **Authentication**: JWT
- **Security**: Helmet, CORS, Rate Limiting
- **Validation**: Express Validator
- **Password Hashing**: bcryptjs
- **Date Handling**: date-fns
- **Utilities**: UUID, Compression

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lend_flow_loan_api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Build the project**
   ```bash
   npm run build
   ```

5. **Start the server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `3000` |
| `NODE_ENV` | Environment | `development` |
| `JWT_SECRET` | JWT signing secret | Required |
| `JWT_EXPIRES_IN` | Token expiration | `7d` |
| `RATE_LIMIT_MAX_REQUESTS` | Rate limit | `100` |
| `CORS_ORIGIN` | CORS origin | `*` |

See `.env.example` for complete configuration options.

## 📚 API Documentation

### Base URL
```
http://localhost:3000/api/v1
```

### Authentication
Most endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Demo Credentials
- **Phone**: `0790775800`
- **PIN**: `9999`

## 🔗 API Endpoints

### Authentication (`/auth`)
- `POST /login` - Login with phone and PIN
- `POST /send-otp` - Send OTP for verification
- `POST /verify-otp` - Verify OTP
- `POST /reset-pin` - Reset PIN with OTP
- `GET /me` - Get current user
- `POST /change-pin` - Change PIN
- `POST /refresh-token` - Refresh JWT token
- `POST /logout` - Logout

### Loans (`/loans`)
- `POST /applications` - Create loan application
- `GET /applications` - Get loan applications
- `GET /applications/:id` - Get specific application
- `PATCH /applications/:id/cancel` - Cancel application
- `GET /` - Get user loans
- `GET /summary` - Get loan summary
- `GET /:id` - Get specific loan
- `POST /calculate` - Calculate loan details

### Payments (`/payments`)
- `POST /` - Create payment
- `GET /` - Get user payments
- `GET /summary` - Get payment summary
- `GET /:id` - Get specific payment
- `GET /loans/:loanId` - Get loan payments
- `GET /loans/:loanId/schedule` - Get payment schedule
- `GET /loans/:loanId/early-payoff` - Calculate early payoff

### Profile (`/profile`)
- `GET /` - Get user profile
- `PATCH /` - Update profile
- `DELETE /` - Delete account
- `GET /financial-summary` - Get financial summary
- `GET /notifications` - Get notifications
- `PATCH /notifications/:id/read` - Mark notification as read
- `GET /notification-preferences` - Get preferences
- `PATCH /notification-preferences` - Update preferences

### Loan Settings (`/loan-settings`)
- `GET /` - Get loan settings
- `GET /eligibility-criteria` - Get eligibility criteria
- `POST /check-eligibility` - Check eligibility
- `GET /interest-rate` - Get interest rate

## 📊 Data Models

### User
```typescript
interface User {
  id: string;
  phoneNumber: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  creditScore?: number;
  isActive: boolean;
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### Loan
```typescript
interface Loan {
  id: string;
  userId: string;
  amount: number;
  interestRate: number;
  termMonths: number;
  monthlyPayment: number;
  status: LoanStatus;
  remainingBalance: number;
  // ... more fields
}
```

### Payment
```typescript
interface Payment {
  id: string;
  loanId: string;
  amount: number;
  principalAmount: number;
  interestAmount: number;
  paymentDate: Date;
  status: PaymentStatus;
  // ... more fields
}
```

## 🧪 Testing

### Manual Testing
1. Start the server: `npm run dev`
2. Use the demo credentials to login
3. Test endpoints using Postman or curl

### Example Login Request
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumber": "0790775800",
    "pin": "9999"
  }'
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based auth
- **Rate Limiting**: Prevents abuse
- **Input Validation**: Comprehensive validation
- **Security Headers**: Helmet.js protection
- **CORS**: Configurable cross-origin requests
- **PIN Hashing**: Secure PIN storage

## 🚀 Deployment

### Production Checklist
- [ ] Set strong JWT secret
- [ ] Configure proper CORS origins
- [ ] Set up SSL/TLS
- [ ] Configure rate limiting
- [ ] Set up logging
- [ ] Configure database
- [ ] Set up monitoring

### Docker Deployment
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the API documentation at `/api/v1/docs`

## 🔄 Changelog

### v1.0.0
- Initial release
- Complete loan management functionality
- Authentication and security
- Payment processing
- Profile management
- Notification system
