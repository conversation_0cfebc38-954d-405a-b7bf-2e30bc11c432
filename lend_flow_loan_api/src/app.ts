import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';
import { config, isDevelopment } from './config/env';
import { database } from './config/database';
import { generateToken } from './middleware/auth';

const app = express();

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Lend Flow Loan API',
      version: '1.0.0',
      description: 'A comprehensive RESTful API for loan management, built with Node.js, Express, and TypeScript. This API provides complete loan lifecycle management including applications, approvals, payments, and user profile management.',
      contact: {
        name: 'Lend Flow API Support',
        email: '<EMAIL>'
      },
      servers: [
        {
          url: `http://localhost:${config.PORT}${config.API_PREFIX}/${config.API_VERSION}`,
          description: 'Development server'
        }
      ]
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['./src/app.ts'] // Path to the API docs
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: isDevelopment() ? false : undefined,
}));

// CORS configuration
app.use(cors({
  origin: config.CORS_ORIGIN,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
if (config.ENABLE_MORGAN_LOGGING) {
  app.use(morgan(isDevelopment() ? 'dev' : 'combined'));
}

// Swagger UI
if (config.ENABLE_SWAGGER) {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
}

// Health check endpoint
/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     description: Returns the health status of the API
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 timestamp:
 *                   type: string
 *                 version:
 *                   type: string
 *                 environment:
 *                   type: string
 */
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Lend Flow API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: config.NODE_ENV,
  });
});

// Authentication middleware
const authenticateToken = (req: any, res: any, next: any) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token required'
    });
  }

  try {
    // For demo purposes, we'll accept any token that starts with 'demo-'
    if (token.startsWith('demo-')) {
      req.user = { id: 'user-**********', phoneNumber: '**********' };
      next();
    } else {
      res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }
  } catch (error) {
    res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  }
};

// API routes
const apiPrefix = `${config.API_PREFIX}/${config.API_VERSION}`;

// Auth endpoints
/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: User login
 *     description: Authenticate user with phone number and PIN
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phoneNumber
 *               - pin
 *             properties:
 *               phoneNumber:
 *                 type: string
 *                 description: User's phone number
 *                 example: "**********"
 *               pin:
 *                 type: string
 *                 description: User's PIN
 *                 example: "9999"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         phoneNumber:
 *                           type: string
 *                         firstName:
 *                           type: string
 *                         lastName:
 *                           type: string
 *                         email:
 *                           type: string
 *                     token:
 *                       type: string
 *                 message:
 *                   type: string
 *       401:
 *         description: Invalid credentials
 */
app.post(`${apiPrefix}/auth/login`, (req, res) => {
  const { phoneNumber, pin } = req.body;

  if (phoneNumber === '**********' && pin === '9999') {
    const token = 'demo-token-' + Date.now();
    
    res.json({
      success: true,
      data: {
        user: {
          id: 'user-**********',
          phoneNumber: '**********',
          firstName: 'Demo',
          lastName: 'User',
          email: '<EMAIL>'
        },
        token
      },
      message: 'Login successful'
    });
  } else {
    res.status(401).json({
      success: false,
      error: 'Invalid credentials'
    });
  }
});

// Loan endpoints
/**
 * @swagger
 * /loans:
 *   get:
 *     summary: Get user loans
 *     description: Retrieve all loans for the authenticated user
 *     tags: [Loans]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of user loans
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       amount:
 *                         type: number
 *                       status:
 *                         type: string
 *                       remainingBalance:
 *                         type: number
 *       401:
 *         description: Unauthorized
 */
app.get(`${apiPrefix}/loans`, authenticateToken, (req: any, res: any) => {
  const loans = database.getLoansByUserId(req.user.id);
  res.json({
    success: true,
    data: loans
  });
});

/**
 * @swagger
 * /loans/summary:
 *   get:
 *     summary: Get loan summary
 *     description: Get a summary of the user's loan statistics
 *     tags: [Loans]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Loan summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalLoans:
 *                       type: number
 *                     activeLoans:
 *                       type: number
 *                     totalOutstanding:
 *                       type: number
 *                     totalBorrowed:
 *                       type: number
 *       401:
 *         description: Unauthorized
 */
app.get(`${apiPrefix}/loans/summary`, authenticateToken, (req: any, res: any) => {
  const loans = database.getLoansByUserId(req.user.id);
  const activeLoans = loans.filter((loan: any) => loan.status === 'active');

  const summary = {
    totalLoans: loans.length,
    activeLoans: activeLoans.length,
    totalOutstanding: activeLoans.reduce((sum: number, loan: any) => sum + loan.remainingBalance, 0),
    totalBorrowed: loans.reduce((sum: number, loan: any) => sum + loan.amount, 0)
  };

  res.json({
    success: true,
    data: summary
  });
});

/**
 * @swagger
 * /loans/calculate:
 *   post:
 *     summary: Calculate loan payments
 *     description: Calculate monthly payment and total interest for a loan
 *     tags: [Loans]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - termMonths
 *             properties:
 *               amount:
 *                 type: number
 *                 description: Loan amount
 *                 example: 10000
 *               termMonths:
 *                 type: number
 *                 description: Loan term in months
 *                 example: 12
 *               interestRate:
 *                 type: number
 *                 description: Annual interest rate (default 12%)
 *                 example: 12
 *     responses:
 *       200:
 *         description: Loan calculation results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     principal:
 *                       type: number
 *                     interestRate:
 *                       type: number
 *                     termMonths:
 *                       type: number
 *                     monthlyPayment:
 *                       type: number
 *                     totalInterest:
 *                       type: number
 *                     totalAmount:
 *                       type: number
 *       400:
 *         description: Bad request - missing required fields
 */
app.post(`${apiPrefix}/loans/calculate`, (req: any, res: any) => {
  const { amount, termMonths, interestRate = 12 } = req.body;

  if (!amount || !termMonths) {
    res.status(400).json({
      success: false,
      error: 'Amount and term are required'
    });
    return;
  }

  const monthlyRate = interestRate / 100 / 12;
  let monthlyPayment: number;

  if (monthlyRate === 0) {
    monthlyPayment = amount / termMonths;
  } else {
    const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
    const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
    monthlyPayment = amount * (numerator / denominator);
  }

  const totalAmount = monthlyPayment * termMonths;
  const totalInterest = totalAmount - amount;

  res.json({
    success: true,
    data: {
      principal: amount,
      interestRate,
      termMonths,
      monthlyPayment: Math.round(monthlyPayment * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      totalAmount: Math.round(totalAmount * 100) / 100
    }
  });
});

// Payment endpoints
/**
 * @swagger
 * /payments:
 *   get:
 *     summary: Get user payments
 *     description: Retrieve all payments for the authenticated user
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of user payments
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       amount:
 *                         type: number
 *                       status:
 *                         type: string
 *                       paymentDate:
 *                         type: string
 *       401:
 *         description: Unauthorized
 */
app.get(`${apiPrefix}/payments`, authenticateToken, (req: any, res: any) => {
  const payments = database.getPaymentsByUserId(req.user.id);
  res.json({
    success: true,
    data: payments
  });
});

// Profile endpoints
/**
 * @swagger
 * /profile:
 *   get:
 *     summary: Get user profile
 *     description: Retrieve the authenticated user's profile information
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     firstName:
 *                       type: string
 *                     lastName:
 *                       type: string
 *                     phoneNumber:
 *                       type: string
 *                     email:
 *                       type: string
 *       401:
 *         description: Unauthorized
 */
app.get(`${apiPrefix}/profile`, authenticateToken, (req: any, res: any) => {
  const user = database.getUserById(req.user.id);
  res.json({
    success: true,
    data: user
  });
});

// Loan settings endpoints
/**
 * @swagger
 * /loan-settings:
 *   get:
 *     summary: Get loan settings
 *     description: Retrieve current loan application settings and limits
 *     tags: [Settings]
 *     responses:
 *       200:
 *         description: Loan settings
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     minLoanAmount:
 *                       type: number
 *                     maxLoanAmount:
 *                       type: number
 *                     minTermMonths:
 *                       type: number
 *                     maxTermMonths:
 *                       type: number
 *                     baseInterestRate:
 *                       type: number
 *                     maxInterestRate:
 *                       type: number
 */
app.get(`${apiPrefix}/loan-settings`, (req, res) => {
  const settings = database.getLoanSettings();
  res.json({
    success: true,
    data: settings
  });
});

// API documentation endpoint
app.get(`${apiPrefix}/docs`, (req, res) => {
  res.json({
    success: true,
    message: 'Lend Flow API Documentation',
    version: '1.0.0',
    endpoints: {
      'POST /auth/login': 'Login with phone number and PIN',
      'GET /loans': 'Get user loans',
      'GET /loans/summary': 'Get loan summary',
      'POST /loans/calculate': 'Calculate loan details',
      'GET /payments': 'Get user payments',
      'GET /profile': 'Get user profile',
      'GET /loan-settings': 'Get loan settings'
    },
    demoCredentials: {
      phoneNumber: '**********',
      pin: '9999'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `The requested endpoint ${req.method} ${req.originalUrl} was not found.`,
  });
});

// Global error handler
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', error);

  res.status(error.status || 500).json({
    success: false,
    error: isDevelopment() ? error.message : 'Internal server error',
    ...(isDevelopment() && { stack: error.stack }),
  });
});

export default app;
