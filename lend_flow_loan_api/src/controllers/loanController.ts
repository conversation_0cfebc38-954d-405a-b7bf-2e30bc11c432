import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { database } from '../config/database';
import { loanService } from '../services/loanService';
import { 
  Loan, 
  LoanApplication, 
  CreateLoanApplicationRequest, 
  UpdateLoanRequest,
  ApiResponse,
  PaginationInfo
} from '../types';

export class LoanController {
  // Create a new loan application
  async createLoanApplication(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const applicationData: CreateLoanApplicationRequest = req.body;

      // Check if user has any pending applications
      const existingApplications = database.getLoanApplicationsByUserId(userId);
      const pendingApplication = existingApplications.find(
        app => app.status === 'submitted' || app.status === 'under_review'
      );

      if (pendingApplication) {
        res.status(400).json({
          success: false,
          error: 'You already have a pending loan application'
        });
        return;
      }

      // Create loan application
      const application: LoanApplication = {
        id: uuidv4(),
        userId,
        requestedAmount: applicationData.requestedAmount,
        purpose: applicationData.purpose,
        termMonths: applicationData.termMonths,
        employmentInfo: applicationData.employmentInfo,
        monthlyIncome: applicationData.monthlyIncome,
        existingDebts: applicationData.existingDebts,
        status: 'submitted',
        documents: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const createdApplication = database.createLoanApplication(application);

      // Perform risk assessment
      const riskAssessment = await loanService.performRiskAssessment(createdApplication);
      
      // Auto-approve or reject based on risk assessment
      const updatedApplication = await loanService.processApplication(createdApplication.id);

      res.status(201).json({
        success: true,
        data: updatedApplication,
        message: 'Loan application submitted successfully'
      });
    } catch (error) {
      console.error('Error creating loan application:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create loan application'
      });
    }
  }

  // Get user's loan applications
  async getLoanApplications(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      const allApplications = database.getLoanApplicationsByUserId(userId);
      const total = allApplications.length;
      const applications = allApplications.slice(offset, offset + limit);

      const pagination: PaginationInfo = {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      };

      res.json({
        success: true,
        data: applications,
        pagination
      });
    } catch (error) {
      console.error('Error fetching loan applications:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch loan applications'
      });
    }
  }

  // Get specific loan application
  async getLoanApplication(req: Request, res: Response): Promise<void> {
    try {
      const { applicationId } = req.params;
      const userId = req.user!.id;

      const application = database.getLoanApplicationById(applicationId);

      if (!application) {
        res.status(404).json({
          success: false,
          error: 'Loan application not found'
        });
        return;
      }

      if (application.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      res.json({
        success: true,
        data: application
      });
    } catch (error) {
      console.error('Error fetching loan application:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch loan application'
      });
    }
  }

  // Get user's loans
  async getLoans(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const status = req.query.status as string;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      let loans = database.getLoansByUserId(userId);

      // Filter by status if provided
      if (status) {
        loans = loans.filter(loan => loan.status === status);
      }

      const total = loans.length;
      const paginatedLoans = loans.slice(offset, offset + limit);

      const pagination: PaginationInfo = {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      };

      res.json({
        success: true,
        data: paginatedLoans,
        pagination
      });
    } catch (error) {
      console.error('Error fetching loans:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch loans'
      });
    }
  }

  // Get specific loan
  async getLoan(req: Request, res: Response): Promise<void> {
    try {
      const { loanId } = req.params;
      const userId = req.user!.id;

      const loan = database.getLoanById(loanId);

      if (!loan) {
        res.status(404).json({
          success: false,
          error: 'Loan not found'
        });
        return;
      }

      if (loan.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      res.json({
        success: true,
        data: loan
      });
    } catch (error) {
      console.error('Error fetching loan:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch loan'
      });
    }
  }

  // Update loan (admin function)
  async updateLoan(req: Request, res: Response): Promise<void> {
    try {
      const { loanId } = req.params;
      const updates: UpdateLoanRequest = req.body;

      const loan = database.getLoanById(loanId);

      if (!loan) {
        res.status(404).json({
          success: false,
          error: 'Loan not found'
        });
        return;
      }

      const updatedLoan = database.updateLoan(loanId, updates);

      res.json({
        success: true,
        data: updatedLoan,
        message: 'Loan updated successfully'
      });
    } catch (error) {
      console.error('Error updating loan:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update loan'
      });
    }
  }

  // Cancel loan application
  async cancelLoanApplication(req: Request, res: Response): Promise<void> {
    try {
      const { applicationId } = req.params;
      const userId = req.user!.id;

      const application = database.getLoanApplicationById(applicationId);

      if (!application) {
        res.status(404).json({
          success: false,
          error: 'Loan application not found'
        });
        return;
      }

      if (application.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      if (application.status !== 'submitted' && application.status !== 'under_review') {
        res.status(400).json({
          success: false,
          error: 'Cannot cancel application in current status'
        });
        return;
      }

      const updatedApplication = database.updateLoanApplication(applicationId, {
        status: 'cancelled'
      });

      res.json({
        success: true,
        data: updatedApplication,
        message: 'Loan application cancelled successfully'
      });
    } catch (error) {
      console.error('Error cancelling loan application:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to cancel loan application'
      });
    }
  }

  // Get loan summary for dashboard
  async getLoanSummary(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const loans = database.getLoansByUserId(userId);

      const summary = {
        totalLoans: loans.length,
        activeLoans: loans.filter(loan => loan.status === 'active').length,
        completedLoans: loans.filter(loan => loan.status === 'completed').length,
        totalBorrowed: loans.reduce((sum, loan) => sum + loan.amount, 0),
        totalOutstanding: loans
          .filter(loan => loan.status === 'active')
          .reduce((sum, loan) => sum + loan.remainingBalance, 0),
        totalPaid: loans.reduce((sum, loan) => sum + (loan.amount - loan.remainingBalance), 0),
        nextPaymentDue: loans
          .filter(loan => loan.status === 'active' && loan.nextPaymentDate)
          .sort((a, b) => new Date(a.nextPaymentDate!).getTime() - new Date(b.nextPaymentDate!).getTime())[0]
      };

      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      console.error('Error fetching loan summary:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch loan summary'
      });
    }
  }

  // Calculate loan details
  async calculateLoan(req: Request, res: Response): Promise<void> {
    try {
      const { amount, termMonths, interestRate } = req.body;

      if (!amount || !termMonths) {
        res.status(400).json({
          success: false,
          error: 'Amount and term are required'
        });
        return;
      }

      const calculation = loanService.calculateLoanDetails(
        amount,
        interestRate || 12, // Default interest rate
        termMonths
      );

      res.json({
        success: true,
        data: calculation
      });
    } catch (error) {
      console.error('Error calculating loan:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to calculate loan'
      });
    }
  }
}

export const loanController = new LoanController();
