import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import { database } from '../config/database';
import { generateToken } from '../middleware/auth';
import { User } from '../types';

export class AuthController {
  // Login with phone number and PIN
  async login(req: Request, res: Response): Promise<void> {
    try {
      const { phoneNumber, pin } = req.body;

      if (!phoneNumber || !pin) {
        res.status(400).json({
          success: false,
          error: 'Phone number and PIN are required'
        });
        return;
      }

      // Find user by phone number
      let user = database.getUserByPhone(phoneNumber);

      // If user doesn't exist, create them (no signup required)
      if (!user) {
        const hashedPin = await bcrypt.hash(pin, 12);
        
        const newUser: User = {
          id: uuidv4(),
          phoneNumber,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        user = database.createUser(newUser);
        
        // Store PIN separately (in real app, this would be in a secure table)
        // For demo purposes, we'll just validate against the demo PIN
      }

      if (!user.isActive) {
        res.status(401).json({
          success: false,
          error: 'Account is deactivated'
        });
        return;
      }

      // Validate PIN (simplified for demo)
      // In production, you would hash and compare PINs properly
      const isValidPin = phoneNumber === '**********' ? pin === '9999' : true;

      if (!isValidPin) {
        res.status(401).json({
          success: false,
          error: 'Invalid PIN'
        });
        return;
      }

      // Generate JWT token
      const token = generateToken({
        id: user.id,
        phoneNumber: user.phoneNumber,
        role: 'user'
      });

      // Update last login
      database.updateUser(user.id, {
        updatedAt: new Date()
      });

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            phoneNumber: user.phoneNumber,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            isVerified: user.isVerified,
            createdAt: user.createdAt
          },
          token
        },
        message: 'Login successful'
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: 'Login failed'
      });
    }
  }

  // Verify phone number (send OTP)
  async sendOTP(req: Request, res: Response): Promise<void> {
    try {
      const { phoneNumber } = req.body;

      if (!phoneNumber) {
        res.status(400).json({
          success: false,
          error: 'Phone number is required'
        });
        return;
      }

      // In a real app, you would:
      // 1. Generate a random OTP
      // 2. Store it temporarily (Redis/database)
      // 3. Send it via SMS service
      
      // For demo purposes, we'll just return success
      const otp = '123456'; // Mock OTP

      console.log(`OTP for ${phoneNumber}: ${otp}`);

      res.json({
        success: true,
        message: 'OTP sent successfully',
        data: {
          phoneNumber,
          // In production, never return the actual OTP
          otp: process.env.NODE_ENV === 'development' ? otp : undefined
        }
      });
    } catch (error) {
      console.error('Send OTP error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to send OTP'
      });
    }
  }

  // Verify OTP
  async verifyOTP(req: Request, res: Response): Promise<void> {
    try {
      const { phoneNumber, otp } = req.body;

      if (!phoneNumber || !otp) {
        res.status(400).json({
          success: false,
          error: 'Phone number and OTP are required'
        });
        return;
      }

      // In a real app, you would verify the OTP against stored value
      // For demo purposes, accept any OTP
      const isValidOTP = otp === '123456' || otp.length === 6;

      if (!isValidOTP) {
        res.status(400).json({
          success: false,
          error: 'Invalid OTP'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Phone number verified successfully',
        data: {
          phoneNumber,
          verified: true
        }
      });
    } catch (error) {
      console.error('Verify OTP error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to verify OTP'
      });
    }
  }

  // Change PIN
  async changePIN(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const { currentPin, newPin } = req.body;

      if (!currentPin || !newPin) {
        res.status(400).json({
          success: false,
          error: 'Current PIN and new PIN are required'
        });
        return;
      }

      if (newPin.length !== 4 || !/^\d{4}$/.test(newPin)) {
        res.status(400).json({
          success: false,
          error: 'PIN must be exactly 4 digits'
        });
        return;
      }

      const user = database.getUserById(userId);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      // In a real app, you would verify the current PIN
      // For demo purposes, we'll just accept the change
      
      // Hash the new PIN
      const hashedPin = await bcrypt.hash(newPin, 12);

      // In a real app, you would store this in a secure pins table
      console.log(`PIN changed for user ${userId}: ${hashedPin}`);

      res.json({
        success: true,
        message: 'PIN changed successfully'
      });
    } catch (error) {
      console.error('Change PIN error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to change PIN'
      });
    }
  }

  // Reset PIN (requires phone verification)
  async resetPIN(req: Request, res: Response): Promise<void> {
    try {
      const { phoneNumber, otp, newPin } = req.body;

      if (!phoneNumber || !otp || !newPin) {
        res.status(400).json({
          success: false,
          error: 'Phone number, OTP, and new PIN are required'
        });
        return;
      }

      if (newPin.length !== 4 || !/^\d{4}$/.test(newPin)) {
        res.status(400).json({
          success: false,
          error: 'PIN must be exactly 4 digits'
        });
        return;
      }

      // Verify OTP (simplified for demo)
      const isValidOTP = otp === '123456';

      if (!isValidOTP) {
        res.status(400).json({
          success: false,
          error: 'Invalid OTP'
        });
        return;
      }

      const user = database.getUserByPhone(phoneNumber);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      // Hash the new PIN
      const hashedPin = await bcrypt.hash(newPin, 12);

      // In a real app, you would store this in a secure pins table
      console.log(`PIN reset for user ${user.id}: ${hashedPin}`);

      res.json({
        success: true,
        message: 'PIN reset successfully'
      });
    } catch (error) {
      console.error('Reset PIN error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to reset PIN'
      });
    }
  }

  // Refresh token
  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const user = database.getUserById(userId);

      if (!user || !user.isActive) {
        res.status(401).json({
          success: false,
          error: 'Invalid user'
        });
        return;
      }

      // Generate new token
      const token = generateToken({
        id: user.id,
        phoneNumber: user.phoneNumber,
        role: 'user'
      });

      res.json({
        success: true,
        data: { token },
        message: 'Token refreshed successfully'
      });
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to refresh token'
      });
    }
  }

  // Logout (client-side token removal)
  async logout(req: Request, res: Response): Promise<void> {
    try {
      // In a real app with token blacklisting, you would add the token to a blacklist
      // For JWT tokens, logout is typically handled client-side by removing the token
      
      res.json({
        success: true,
        message: 'Logged out successfully'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: 'Logout failed'
      });
    }
  }

  // Get current user info
  async getCurrentUser(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const user = database.getUserById(userId);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          id: user.id,
          phoneNumber: user.phoneNumber,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          isVerified: user.isVerified,
          isActive: user.isActive,
          createdAt: user.createdAt
        }
      });
    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get user info'
      });
    }
  }
}

export const authController = new AuthController();
