import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { database } from '../config/database';
import { paymentService } from '../services/paymentService';
import { 
  Payment, 
  CreatePaymentRequest, 
  ApiResponse,
  PaginationInfo
} from '../types';

export class PaymentController {
  // Create a new payment
  async createPayment(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const paymentData: CreatePaymentRequest = req.body;

      // Verify loan belongs to user
      const loan = database.getLoanById(paymentData.loanId);
      
      if (!loan) {
        res.status(404).json({
          success: false,
          error: 'Loan not found'
        });
        return;
      }

      if (loan.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      if (loan.status !== 'active') {
        res.status(400).json({
          success: false,
          error: 'Cannot make payment on inactive loan'
        });
        return;
      }

      // Process payment
      const payment = await paymentService.processPayment(paymentData, userId);

      res.status(201).json({
        success: true,
        data: payment,
        message: 'Payment processed successfully'
      });
    } catch (error) {
      console.error('Error creating payment:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process payment'
      });
    }
  }

  // Get payments for a specific loan
  async getLoanPayments(req: Request, res: Response): Promise<void> {
    try {
      const { loanId } = req.params;
      const userId = req.user!.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Verify loan belongs to user
      const loan = database.getLoanById(loanId);
      
      if (!loan) {
        res.status(404).json({
          success: false,
          error: 'Loan not found'
        });
        return;
      }

      if (loan.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      const allPayments = database.getPaymentsByLoanId(loanId);
      const total = allPayments.length;
      const payments = allPayments
        .sort((a, b) => b.paymentDate.getTime() - a.paymentDate.getTime())
        .slice(offset, offset + limit);

      const pagination: PaginationInfo = {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      };

      res.json({
        success: true,
        data: payments,
        pagination
      });
    } catch (error) {
      console.error('Error fetching loan payments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch payments'
      });
    }
  }

  // Get all payments for user
  async getUserPayments(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const status = req.query.status as string;
      const offset = (page - 1) * limit;

      let payments = database.getPaymentsByUserId(userId);

      // Filter by status if provided
      if (status) {
        payments = payments.filter(payment => payment.status === status);
      }

      const total = payments.length;
      const paginatedPayments = payments
        .sort((a, b) => b.paymentDate.getTime() - a.paymentDate.getTime())
        .slice(offset, offset + limit);

      const pagination: PaginationInfo = {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      };

      res.json({
        success: true,
        data: paginatedPayments,
        pagination
      });
    } catch (error) {
      console.error('Error fetching user payments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch payments'
      });
    }
  }

  // Get payment schedule for a loan
  async getPaymentSchedule(req: Request, res: Response): Promise<void> {
    try {
      const { loanId } = req.params;
      const userId = req.user!.id;

      // Verify loan belongs to user
      const loan = database.getLoanById(loanId);
      
      if (!loan) {
        res.status(404).json({
          success: false,
          error: 'Loan not found'
        });
        return;
      }

      if (loan.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      const schedule = database.getPaymentSchedulesByLoanId(loanId);

      res.json({
        success: true,
        data: schedule
      });
    } catch (error) {
      console.error('Error fetching payment schedule:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch payment schedule'
      });
    }
  }

  // Get specific payment
  async getPayment(req: Request, res: Response): Promise<void> {
    try {
      const { paymentId } = req.params;
      const userId = req.user!.id;

      const payment = database.getPaymentById(paymentId);

      if (!payment) {
        res.status(404).json({
          success: false,
          error: 'Payment not found'
        });
        return;
      }

      // Verify payment belongs to user's loan
      const loan = database.getLoanById(payment.loanId);
      
      if (!loan || loan.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      res.json({
        success: true,
        data: payment
      });
    } catch (error) {
      console.error('Error fetching payment:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch payment'
      });
    }
  }

  // Get payment summary for dashboard
  async getPaymentSummary(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const payments = database.getPaymentsByUserId(userId);
      const loans = database.getLoansByUserId(userId);

      const currentDate = new Date();
      const currentMonth = currentDate.getMonth();
      const currentYear = currentDate.getFullYear();

      const summary = {
        totalPayments: payments.length,
        totalAmountPaid: payments
          .filter(p => p.status === 'completed')
          .reduce((sum, payment) => sum + payment.amount, 0),
        paymentsThisMonth: payments.filter(payment => {
          const paymentDate = new Date(payment.paymentDate);
          return paymentDate.getMonth() === currentMonth && 
                 paymentDate.getFullYear() === currentYear &&
                 payment.status === 'completed';
        }).length,
        pendingPayments: payments.filter(p => p.status === 'pending').length,
        overduePayments: payments.filter(p => {
          return p.status === 'pending' && new Date(p.dueDate) < currentDate;
        }).length,
        nextPaymentDue: this.getNextPaymentDue(loans),
        recentPayments: payments
          .filter(p => p.status === 'completed')
          .sort((a, b) => b.paymentDate.getTime() - a.paymentDate.getTime())
          .slice(0, 5)
      };

      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      console.error('Error fetching payment summary:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch payment summary'
      });
    }
  }

  // Calculate early payoff amount
  async calculateEarlyPayoff(req: Request, res: Response): Promise<void> {
    try {
      const { loanId } = req.params;
      const userId = req.user!.id;

      const loan = database.getLoanById(loanId);
      
      if (!loan) {
        res.status(404).json({
          success: false,
          error: 'Loan not found'
        });
        return;
      }

      if (loan.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      if (loan.status !== 'active') {
        res.status(400).json({
          success: false,
          error: 'Loan is not active'
        });
        return;
      }

      const payoffCalculation = paymentService.calculateEarlyPayoff(loan);

      res.json({
        success: true,
        data: payoffCalculation
      });
    } catch (error) {
      console.error('Error calculating early payoff:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to calculate early payoff'
      });
    }
  }

  private getNextPaymentDue(loans: any[]) {
    const activeLoans = loans.filter(loan => loan.status === 'active');
    
    if (activeLoans.length === 0) return null;

    const nextPayments = activeLoans
      .filter(loan => loan.nextPaymentDate)
      .map(loan => ({
        loanId: loan.id,
        amount: loan.monthlyPayment,
        dueDate: loan.nextPaymentDate,
        loanPurpose: loan.purpose
      }))
      .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

    return nextPayments[0] || null;
  }
}

export const paymentController = new PaymentController();
