import { Request, Response } from 'express';
import { database } from '../config/database';
import { paymentService } from '../services/paymentService';
import { notificationService } from '../services/notificationService';
import { 
  UpdateProfileRequest,
  ApiResponse
} from '../types';

export class ProfileController {
  // Get user profile
  async getProfile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const user = database.getUserById(userId);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      // Remove sensitive information before sending
      const { ...userProfile } = user;

      res.json({
        success: true,
        data: userProfile
      });
    } catch (error) {
      console.error('Error fetching profile:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch profile'
      });
    }
  }

  // Update user profile
  async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const updates: UpdateProfileRequest = req.body;

      const user = database.getUserById(userId);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      // Update user profile
      const updatedUser = database.updateUser(userId, {
        ...updates,
        updatedAt: new Date()
      });

      if (!updatedUser) {
        res.status(500).json({
          success: false,
          error: 'Failed to update profile'
        });
        return;
      }

      res.json({
        success: true,
        data: updatedUser,
        message: 'Profile updated successfully'
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update profile'
      });
    }
  }

  // Get user's financial summary
  async getFinancialSummary(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      
      const loans = database.getLoansByUserId(userId);
      const payments = database.getPaymentsByUserId(userId);
      const paymentStats = paymentService.getPaymentStatistics(userId);

      const summary = {
        // Loan statistics
        totalLoans: loans.length,
        activeLoans: loans.filter(loan => loan.status === 'active').length,
        completedLoans: loans.filter(loan => loan.status === 'completed').length,
        
        // Financial statistics
        totalBorrowed: loans.reduce((sum, loan) => sum + loan.amount, 0),
        totalOutstanding: loans
          .filter(loan => loan.status === 'active')
          .reduce((sum, loan) => sum + loan.remainingBalance, 0),
        totalRepaid: loans.reduce((sum, loan) => sum + (loan.amount - loan.remainingBalance), 0),
        
        // Payment statistics
        ...paymentStats,
        
        // Credit information
        creditScore: database.getUserById(userId)?.creditScore || null,
        
        // Next payment information
        nextPaymentDue: this.getNextPaymentInfo(loans),
        
        // Recent activity
        recentLoans: loans
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
          .slice(0, 3),
        recentPayments: payments
          .filter(p => p.status === 'completed')
          .sort((a, b) => b.paymentDate.getTime() - a.paymentDate.getTime())
          .slice(0, 5)
      };

      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      console.error('Error fetching financial summary:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch financial summary'
      });
    }
  }

  // Get user's notifications
  async getNotifications(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const unreadOnly = req.query.unreadOnly === 'true';
      const offset = (page - 1) * limit;

      let notifications = database.getNotificationsByUserId(userId);

      if (unreadOnly) {
        notifications = notifications.filter(n => !n.isRead);
      }

      const total = notifications.length;
      const paginatedNotifications = notifications.slice(offset, offset + limit);

      const pagination = {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      };

      res.json({
        success: true,
        data: paginatedNotifications,
        pagination,
        meta: {
          unreadCount: notificationService.getUnreadCount(userId)
        }
      });
    } catch (error) {
      console.error('Error fetching notifications:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch notifications'
      });
    }
  }

  // Mark notification as read
  async markNotificationAsRead(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const { notificationId } = req.params;

      const success = await notificationService.markAsRead(notificationId, userId);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'Notification not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Notification marked as read'
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to mark notification as read'
      });
    }
  }

  // Mark all notifications as read
  async markAllNotificationsAsRead(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const markedCount = await notificationService.markAllAsRead(userId);

      res.json({
        success: true,
        message: `${markedCount} notifications marked as read`
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to mark notifications as read'
      });
    }
  }

  // Get notification preferences
  async getNotificationPreferences(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const preferences = await notificationService.getNotificationPreferences(userId);

      res.json({
        success: true,
        data: preferences
      });
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch notification preferences'
      });
    }
  }

  // Update notification preferences
  async updateNotificationPreferences(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const preferences = req.body;

      const success = await notificationService.updateNotificationPreferences(userId, preferences);

      if (!success) {
        res.status(500).json({
          success: false,
          error: 'Failed to update notification preferences'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Notification preferences updated successfully'
      });
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update notification preferences'
      });
    }
  }

  // Delete user account (soft delete)
  async deleteAccount(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      
      // Check if user has active loans
      const activeLoans = database.getLoansByUserId(userId)
        .filter(loan => loan.status === 'active');

      if (activeLoans.length > 0) {
        res.status(400).json({
          success: false,
          error: 'Cannot delete account with active loans. Please complete all loan payments first.'
        });
        return;
      }

      // Soft delete user (mark as inactive)
      const updatedUser = database.updateUser(userId, {
        isActive: false,
        updatedAt: new Date()
      });

      if (!updatedUser) {
        res.status(500).json({
          success: false,
          error: 'Failed to delete account'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Account deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting account:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete account'
      });
    }
  }

  // Get account verification status
  async getVerificationStatus(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const user = database.getUserById(userId);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      const verificationStatus = {
        isVerified: user.isVerified,
        hasBasicInfo: !!(user.firstName && user.lastName && user.email),
        hasAddress: !!user.address,
        hasBankingInfo: !!user.bankingInfo,
        hasIdDocument: false, // Would check document uploads in real app
        completionPercentage: this.calculateProfileCompletion(user)
      };

      res.json({
        success: true,
        data: verificationStatus
      });
    } catch (error) {
      console.error('Error fetching verification status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch verification status'
      });
    }
  }

  private getNextPaymentInfo(loans: any[]) {
    const activeLoans = loans.filter(loan => loan.status === 'active');
    
    if (activeLoans.length === 0) return null;

    const nextPayments = activeLoans
      .filter(loan => loan.nextPaymentDate)
      .map(loan => ({
        loanId: loan.id,
        amount: loan.monthlyPayment,
        dueDate: loan.nextPaymentDate,
        loanPurpose: loan.purpose,
        remainingBalance: loan.remainingBalance
      }))
      .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

    return nextPayments[0] || null;
  }

  private calculateProfileCompletion(user: any): number {
    let completed = 0;
    const total = 6;

    if (user.firstName && user.lastName) completed++;
    if (user.email) completed++;
    if (user.phoneNumber) completed++;
    if (user.address) completed++;
    if (user.dateOfBirth) completed++;
    if (user.bankingInfo) completed++;

    return Math.round((completed / total) * 100);
  }
}

export const profileController = new ProfileController();
