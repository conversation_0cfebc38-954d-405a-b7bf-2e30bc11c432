import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { database } from '../config/database';
import { LoanSettings } from '../types';

export class LoanSettingsController {
  // Get current loan settings
  async getLoanSettings(req: Request, res: Response): Promise<void> {
    try {
      const settings = database.getLoanSettings();

      if (!settings) {
        res.status(404).json({
          success: false,
          error: 'Loan settings not found'
        });
        return;
      }

      res.json({
        success: true,
        data: settings
      });
    } catch (error) {
      console.error('Error fetching loan settings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch loan settings'
      });
    }
  }

  // Update loan settings (admin only)
  async updateLoanSettings(req: Request, res: Response): Promise<void> {
    try {
      const updates = req.body;
      const currentSettings = database.getLoanSettings();

      if (!currentSettings) {
        // Create new settings if none exist
        const newSettings: LoanSettings = {
          id: uuidv4(),
          minLoanAmount: updates.minLoanAmount || 1000,
          maxLoanAmount: updates.maxLoanAmount || 100000,
          minTermMonths: updates.minTermMonths || 6,
          maxTermMonths: updates.maxTermMonths || 60,
          baseInterestRate: updates.baseInterestRate || 12,
          maxInterestRate: updates.maxInterestRate || 30,
          lateFeePercentage: updates.lateFeePercentage || 5,
          lateFeeFixedAmount: updates.lateFeeFixedAmount || 50,
          gracePeriodDays: updates.gracePeriodDays || 7,
          maxDebtToIncomeRatio: updates.maxDebtToIncomeRatio || 0.4,
          minCreditScore: updates.minCreditScore || 300,
          processingFeePercentage: updates.processingFeePercentage || 2,
          processingFeeFixedAmount: updates.processingFeeFixedAmount || 100,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // In a real database, you would insert this
        res.status(201).json({
          success: true,
          data: newSettings,
          message: 'Loan settings created successfully'
        });
        return;
      }

      const updatedSettings = database.updateLoanSettings(currentSettings.id, {
        ...updates,
        updatedAt: new Date()
      });

      if (!updatedSettings) {
        res.status(500).json({
          success: false,
          error: 'Failed to update loan settings'
        });
        return;
      }

      res.json({
        success: true,
        data: updatedSettings,
        message: 'Loan settings updated successfully'
      });
    } catch (error) {
      console.error('Error updating loan settings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update loan settings'
      });
    }
  }

  // Get loan eligibility criteria
  async getEligibilityCriteria(req: Request, res: Response): Promise<void> {
    try {
      const settings = database.getLoanSettings();

      if (!settings) {
        res.status(404).json({
          success: false,
          error: 'Loan settings not found'
        });
        return;
      }

      const criteria = {
        minLoanAmount: settings.minLoanAmount,
        maxLoanAmount: settings.maxLoanAmount,
        minTermMonths: settings.minTermMonths,
        maxTermMonths: settings.maxTermMonths,
        minCreditScore: settings.minCreditScore,
        maxDebtToIncomeRatio: settings.maxDebtToIncomeRatio,
        baseInterestRate: settings.baseInterestRate,
        maxInterestRate: settings.maxInterestRate,
        processingFeePercentage: settings.processingFeePercentage,
        processingFeeFixedAmount: settings.processingFeeFixedAmount
      };

      res.json({
        success: true,
        data: criteria
      });
    } catch (error) {
      console.error('Error fetching eligibility criteria:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch eligibility criteria'
      });
    }
  }

  // Check loan eligibility for a user
  async checkEligibility(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const { requestedAmount, termMonths, monthlyIncome, existingDebts } = req.body;

      if (!requestedAmount || !termMonths || !monthlyIncome) {
        res.status(400).json({
          success: false,
          error: 'Requested amount, term, and monthly income are required'
        });
        return;
      }

      const settings = database.getLoanSettings();
      const user = database.getUserById(userId);

      if (!settings || !user) {
        res.status(404).json({
          success: false,
          error: 'Settings or user not found'
        });
        return;
      }

      const eligibilityCheck = {
        isEligible: true,
        reasons: [] as string[],
        recommendations: [] as string[],
        maxEligibleAmount: 0,
        suggestedTerms: [] as number[]
      };

      // Check amount limits
      if (requestedAmount < settings.minLoanAmount) {
        eligibilityCheck.isEligible = false;
        eligibilityCheck.reasons.push(`Minimum loan amount is $${settings.minLoanAmount}`);
      }

      if (requestedAmount > settings.maxLoanAmount) {
        eligibilityCheck.isEligible = false;
        eligibilityCheck.reasons.push(`Maximum loan amount is $${settings.maxLoanAmount}`);
      }

      // Check term limits
      if (termMonths < settings.minTermMonths) {
        eligibilityCheck.isEligible = false;
        eligibilityCheck.reasons.push(`Minimum loan term is ${settings.minTermMonths} months`);
      }

      if (termMonths > settings.maxTermMonths) {
        eligibilityCheck.isEligible = false;
        eligibilityCheck.reasons.push(`Maximum loan term is ${settings.maxTermMonths} months`);
      }

      // Check credit score
      const creditScore = user.creditScore || 650;
      if (creditScore < settings.minCreditScore) {
        eligibilityCheck.isEligible = false;
        eligibilityCheck.reasons.push(`Minimum credit score requirement is ${settings.minCreditScore}`);
        eligibilityCheck.recommendations.push('Work on improving your credit score');
      }

      // Calculate debt-to-income ratio
      const monthlyPayment = this.calculateMonthlyPayment(requestedAmount, settings.baseInterestRate, termMonths);
      const totalMonthlyDebts = (existingDebts || 0) + monthlyPayment;
      const debtToIncomeRatio = totalMonthlyDebts / monthlyIncome;

      if (debtToIncomeRatio > settings.maxDebtToIncomeRatio) {
        eligibilityCheck.isEligible = false;
        eligibilityCheck.reasons.push(`Debt-to-income ratio (${(debtToIncomeRatio * 100).toFixed(1)}%) exceeds maximum allowed (${(settings.maxDebtToIncomeRatio * 100)}%)`);
        eligibilityCheck.recommendations.push('Consider reducing existing debts or requesting a smaller loan amount');
      }

      // Calculate maximum eligible amount based on DTI
      const maxMonthlyPayment = (monthlyIncome * settings.maxDebtToIncomeRatio) - (existingDebts || 0);
      if (maxMonthlyPayment > 0) {
        eligibilityCheck.maxEligibleAmount = this.calculateMaxLoanAmount(maxMonthlyPayment, settings.baseInterestRate, termMonths);
      }

      // Suggest alternative terms
      if (!eligibilityCheck.isEligible && requestedAmount <= settings.maxLoanAmount) {
        for (let term = settings.minTermMonths; term <= settings.maxTermMonths; term += 6) {
          const payment = this.calculateMonthlyPayment(requestedAmount, settings.baseInterestRate, term);
          const dti = ((existingDebts || 0) + payment) / monthlyIncome;
          
          if (dti <= settings.maxDebtToIncomeRatio) {
            eligibilityCheck.suggestedTerms.push(term);
          }
        }
      }

      // Add positive recommendations
      if (eligibilityCheck.isEligible) {
        eligibilityCheck.recommendations.push('You meet all eligibility criteria for this loan');
      }

      res.json({
        success: true,
        data: eligibilityCheck
      });
    } catch (error) {
      console.error('Error checking eligibility:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check eligibility'
      });
    }
  }

  // Get interest rate for a user (based on risk assessment)
  async getInterestRate(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const { requestedAmount, termMonths } = req.query;

      const settings = database.getLoanSettings();
      const user = database.getUserById(userId);

      if (!settings || !user) {
        res.status(404).json({
          success: false,
          error: 'Settings or user not found'
        });
        return;
      }

      // Simple risk-based pricing (in real app, this would be more sophisticated)
      const creditScore = user.creditScore || 650;
      let interestRate = settings.baseInterestRate;

      if (creditScore >= 750) {
        interestRate = settings.baseInterestRate;
      } else if (creditScore >= 700) {
        interestRate = settings.baseInterestRate + 1;
      } else if (creditScore >= 650) {
        interestRate = settings.baseInterestRate + 2;
      } else if (creditScore >= 600) {
        interestRate = settings.baseInterestRate + 4;
      } else {
        interestRate = settings.baseInterestRate + 6;
      }

      // Cap at maximum rate
      interestRate = Math.min(interestRate, settings.maxInterestRate);

      const rateInfo = {
        interestRate,
        creditScore,
        riskLevel: this.getRiskLevel(creditScore),
        factors: this.getRateFactors(creditScore, settings.baseInterestRate, interestRate)
      };

      // If amount and term provided, calculate payment details
      if (requestedAmount && termMonths) {
        const amount = parseFloat(requestedAmount as string);
        const term = parseInt(termMonths as string);
        
        const monthlyPayment = this.calculateMonthlyPayment(amount, interestRate, term);
        const totalAmount = monthlyPayment * term;
        const totalInterest = totalAmount - amount;

        (rateInfo as any)['loanDetails'] = {
          requestedAmount: amount,
          termMonths: term,
          monthlyPayment: Math.round(monthlyPayment * 100) / 100,
          totalAmount: Math.round(totalAmount * 100) / 100,
          totalInterest: Math.round(totalInterest * 100) / 100
        };
      }

      res.json({
        success: true,
        data: rateInfo
      });
    } catch (error) {
      console.error('Error getting interest rate:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get interest rate'
      });
    }
  }

  private calculateMonthlyPayment(principal: number, annualRate: number, termMonths: number): number {
    if (annualRate === 0) return principal / termMonths;
    
    const monthlyRate = annualRate / 100 / 12;
    const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
    const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
    
    return principal * (numerator / denominator);
  }

  private calculateMaxLoanAmount(maxMonthlyPayment: number, annualRate: number, termMonths: number): number {
    if (annualRate === 0) return maxMonthlyPayment * termMonths;
    
    const monthlyRate = annualRate / 100 / 12;
    const denominator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
    const numerator = Math.pow(1 + monthlyRate, termMonths) - 1;
    
    return maxMonthlyPayment * (numerator / denominator);
  }

  private getRiskLevel(creditScore: number): string {
    if (creditScore >= 750) return 'Low';
    if (creditScore >= 700) return 'Medium-Low';
    if (creditScore >= 650) return 'Medium';
    if (creditScore >= 600) return 'Medium-High';
    return 'High';
  }

  private getRateFactors(creditScore: number, baseRate: number, finalRate: number) {
    return {
      baseRate,
      creditScoreAdjustment: finalRate - baseRate,
      finalRate,
      explanation: `Rate adjusted based on credit score of ${creditScore}`
    };
  }
}

export const loanSettingsController = new LoanSettingsController();
