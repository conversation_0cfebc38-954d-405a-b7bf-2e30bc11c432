import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { config, validateConfig, isDevelopment } from './config/env';

// Import routes
import authRoutes from './routes/auth';
import loanRoutes from './routes/loans';
import paymentRoutes from './routes/payments';
import profileRoutes from './routes/profile';
import loanSettingsRoutes from './routes/loanSettings';

// Validate configuration
try {
  validateConfig();
} catch (error) {
  console.error('Configuration validation failed:', error);
  process.exit(1);
}

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: isDevelopment() ? false : undefined,
}));

// CORS configuration
app.use(cors({
  origin: config.CORS_ORIGIN,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  max: config.RATE_LIMIT_MAX_REQUESTS,
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
if (config.ENABLE_MORGAN_LOGGING) {
  app.use(morgan(isDevelopment() ? 'dev' : 'combined'));
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Lend Flow API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: config.NODE_ENV,
  });
});

// API routes
const apiPrefix = `${config.API_PREFIX}/${config.API_VERSION}`;

app.use(`${apiPrefix}/auth`, authRoutes);
app.use(`${apiPrefix}/loans`, loanRoutes);
app.use(`${apiPrefix}/payments`, paymentRoutes);
app.use(`${apiPrefix}/profile`, profileRoutes);
app.use(`${apiPrefix}/loan-settings`, loanSettingsRoutes);

// API documentation endpoint
app.get(`${apiPrefix}/docs`, (req, res) => {
  res.json({
    success: true,
    message: 'Lend Flow API Documentation',
    version: '1.0.0',
    endpoints: {
      auth: {
        'POST /auth/login': 'Login with phone number and PIN',
        'POST /auth/send-otp': 'Send OTP for phone verification',
        'POST /auth/verify-otp': 'Verify OTP',
        'POST /auth/reset-pin': 'Reset PIN with OTP',
        'GET /auth/me': 'Get current user info',
        'POST /auth/change-pin': 'Change PIN',
        'POST /auth/refresh-token': 'Refresh JWT token',
        'POST /auth/logout': 'Logout',
      },
      loans: {
        'POST /loans/applications': 'Create loan application',
        'GET /loans/applications': 'Get user loan applications',
        'GET /loans/applications/:id': 'Get specific loan application',
        'PATCH /loans/applications/:id/cancel': 'Cancel loan application',
        'GET /loans': 'Get user loans',
        'GET /loans/summary': 'Get loan summary for dashboard',
        'GET /loans/:id': 'Get specific loan',
        'PATCH /loans/:id': 'Update loan (admin)',
        'POST /loans/calculate': 'Calculate loan details',
      },
      payments: {
        'POST /payments': 'Create payment',
        'GET /payments': 'Get user payments',
        'GET /payments/summary': 'Get payment summary',
        'GET /payments/:id': 'Get specific payment',
        'GET /payments/loans/:loanId': 'Get loan payments',
        'GET /payments/loans/:loanId/schedule': 'Get payment schedule',
        'GET /payments/loans/:loanId/early-payoff': 'Calculate early payoff',
      },
      profile: {
        'GET /profile': 'Get user profile',
        'PATCH /profile': 'Update user profile',
        'DELETE /profile': 'Delete user account',
        'GET /profile/financial-summary': 'Get financial summary',
        'GET /profile/verification-status': 'Get verification status',
        'GET /profile/notifications': 'Get notifications',
        'PATCH /profile/notifications/:id/read': 'Mark notification as read',
        'PATCH /profile/notifications/read-all': 'Mark all notifications as read',
        'GET /profile/notification-preferences': 'Get notification preferences',
        'PATCH /profile/notification-preferences': 'Update notification preferences',
      },
      loanSettings: {
        'GET /loan-settings': 'Get loan settings',
        'GET /loan-settings/eligibility-criteria': 'Get eligibility criteria',
        'POST /loan-settings/check-eligibility': 'Check loan eligibility',
        'GET /loan-settings/interest-rate': 'Get interest rate for user',
      },
    },
    authentication: {
      type: 'Bearer Token',
      header: 'Authorization: Bearer <token>',
      note: 'Most endpoints require authentication. Get token from /auth/login',
    },
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `The requested endpoint ${req.method} ${req.originalUrl} was not found.`,
  });
});

// Global error handler
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', error);

  // Handle specific error types
  if (error.name === 'ValidationError') {
    res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: error.message,
    });
    return;
  }

  if (error.name === 'UnauthorizedError') {
    res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Invalid or expired token',
    });
    return;
  }

  // Default error response
  res.status(error.status || 500).json({
    success: false,
    error: isDevelopment() ? error.message : 'Internal server error',
    ...(isDevelopment() && { stack: error.stack }),
  });
});

// Start server
const PORT = config.PORT;

app.listen(PORT, () => {
  console.log(`
🚀 Lend Flow API Server Started Successfully!

📍 Server Details:
   • Port: ${PORT}
   • Environment: ${config.NODE_ENV}
   • API Version: ${config.API_VERSION}
   • Base URL: http://localhost:${PORT}${apiPrefix}

📚 API Documentation:
   • Docs: http://localhost:${PORT}${apiPrefix}/docs
   • Health: http://localhost:${PORT}/health

🔐 Authentication:
   • Demo Login: POST ${apiPrefix}/auth/login
   • Phone: **********
   • PIN: 9999

🛠️  Available Endpoints:
   • Auth: ${apiPrefix}/auth/*
   • Loans: ${apiPrefix}/loans/*
   • Payments: ${apiPrefix}/payments/*
   • Profile: ${apiPrefix}/profile/*
   • Settings: ${apiPrefix}/loan-settings/*

⚡ Ready to handle requests!
  `);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

export default app;
