import { Router } from 'express';
import { paymentController } from '../controllers/paymentController';
import { authenticateToken } from '../middleware/auth';
import { 
  validatePayment,
  validateUUIDParam,
  validatePaginationQuery,
  handleValidationErrors 
} from '../middleware/validation';

const router = Router();

// All payment routes require authentication
router.use(authenticateToken);

// Payment management routes
router.post('/', validatePayment, paymentController.createPayment);
router.get('/', validatePaginationQuery, handleValidationErrors, paymentController.getUserPayments);
router.get('/summary', paymentController.getPaymentSummary);
router.get('/:paymentId', 
  validateUUIDParam('paymentId'), 
  handleValidationErrors, 
  paymentController.getPayment
);

// Loan-specific payment routes
router.get('/loans/:loanId', 
  validateUUIDParam('loanId'), 
  validatePaginationQuery, 
  handleValidationErrors, 
  paymentController.getLoanPayments
);
router.get('/loans/:loanId/schedule', 
  validateUUIDParam('loanId'), 
  handleValidationErrors, 
  paymentController.getPaymentSchedule
);
router.get('/loans/:loanId/early-payoff', 
  validateUUIDParam('loanId'), 
  handleValidationErrors, 
  paymentController.calculateEarlyPayoff
);

export default router;
