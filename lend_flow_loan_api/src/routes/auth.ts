import { Router } from 'express';
import { authController } from '../controllers/authController';
import { authenticateToken } from '../middleware/auth';
import { 
  validatePhoneNumber, 
  handleValidationErrors 
} from '../middleware/validation';
import { body } from 'express-validator';

const router = Router();

// Validation rules
const validateLogin = [
  ...validatePhoneNumber,
  body('pin')
    .isLength({ min: 4, max: 4 })
    .withMessage('PIN must be exactly 4 digits')
    .isNumeric()
    .withMessage('PIN must contain only numbers'),
  handleValidationErrors
];

const validateOTP = [
  ...validatePhoneNumber,
  body('otp')
    .isLength({ min: 6, max: 6 })
    .withMessage('OTP must be exactly 6 digits')
    .isNumeric()
    .withMessage('OTP must contain only numbers'),
  handleValidationErrors
];

const validateChangePIN = [
  body('currentPin')
    .isLength({ min: 4, max: 4 })
    .withMessage('Current PIN must be exactly 4 digits')
    .isNumeric()
    .withMessage('Current PIN must contain only numbers'),
  body('newPin')
    .isLength({ min: 4, max: 4 })
    .withMessage('New PIN must be exactly 4 digits')
    .isNumeric()
    .withMessage('New PIN must contain only numbers'),
  handleValidationErrors
];

const validateResetPIN = [
  ...validatePhoneNumber,
  body('otp')
    .isLength({ min: 6, max: 6 })
    .withMessage('OTP must be exactly 6 digits')
    .isNumeric()
    .withMessage('OTP must contain only numbers'),
  body('newPin')
    .isLength({ min: 4, max: 4 })
    .withMessage('New PIN must be exactly 4 digits')
    .isNumeric()
    .withMessage('New PIN must contain only numbers'),
  handleValidationErrors
];

// Public routes
router.post('/login', validateLogin, authController.login);
router.post('/send-otp', validatePhoneNumber, handleValidationErrors, authController.sendOTP);
router.post('/verify-otp', validateOTP, authController.verifyOTP);
router.post('/reset-pin', validateResetPIN, authController.resetPIN);

// Protected routes
router.use(authenticateToken);
router.get('/me', authController.getCurrentUser);
router.post('/change-pin', validateChangePIN, authController.changePIN);
router.post('/refresh-token', authController.refreshToken);
router.post('/logout', authController.logout);

export default router;
