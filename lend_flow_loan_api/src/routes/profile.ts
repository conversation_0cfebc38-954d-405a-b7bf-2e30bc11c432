import { Router } from 'express';
import { profileController } from '../controllers/profileController';
import { authenticateToken } from '../middleware/auth';
import { 
  validateUserProfile,
  validateUUIDParam,
  validatePaginationQuery,
  handleValidationErrors 
} from '../middleware/validation';
import { body } from 'express-validator';

const router = Router();

// All profile routes require authentication
router.use(authenticateToken);

// Validation rules for notification preferences
const validateNotificationPreferences = [
  body('email')
    .optional()
    .isBoolean()
    .withMessage('Email preference must be a boolean'),
  body('sms')
    .optional()
    .isBoolean()
    .withMessage('SMS preference must be a boolean'),
  body('push')
    .optional()
    .isBoolean()
    .withMessage('Push preference must be a boolean'),
  body('paymentReminders')
    .optional()
    .isBoolean()
    .withMessage('Payment reminders preference must be a boolean'),
  body('loanUpdates')
    .optional()
    .isBoolean()
    .withMessage('Loan updates preference must be a boolean'),
  body('promotional')
    .optional()
    .isBoolean()
    .withMessage('Promotional preference must be a boolean'),
  handleValidationErrors
];

// Profile management routes
router.get('/', profileController.getProfile);
router.patch('/', validateUserProfile, profileController.updateProfile);
router.delete('/', profileController.deleteAccount);

// Financial summary
router.get('/financial-summary', profileController.getFinancialSummary);

// Verification status
router.get('/verification-status', profileController.getVerificationStatus);

// Notification routes
router.get('/notifications', 
  validatePaginationQuery, 
  handleValidationErrors, 
  profileController.getNotifications
);
router.patch('/notifications/:notificationId/read', 
  validateUUIDParam('notificationId'), 
  handleValidationErrors, 
  profileController.markNotificationAsRead
);
router.patch('/notifications/read-all', profileController.markAllNotificationsAsRead);

// Notification preferences
router.get('/notification-preferences', profileController.getNotificationPreferences);
router.patch('/notification-preferences', 
  validateNotificationPreferences, 
  profileController.updateNotificationPreferences
);

export default router;
