import { Router } from 'express';
import { loanSettingsController } from '../controllers/loanSettingsController';
import { authenticateToken, requireRole } from '../middleware/auth';
import { 
  validateLoanSettings,
  handleValidationErrors 
} from '../middleware/validation';
import { body } from 'express-validator';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// Validation rules for eligibility check
const validateEligibilityCheck = [
  body('requestedAmount')
    .isNumeric()
    .withMessage('Requested amount must be a number')
    .custom((value) => {
      if (value <= 0) {
        throw new Error('Requested amount must be greater than 0');
      }
      return true;
    }),
  body('termMonths')
    .isInt({ min: 1, max: 120 })
    .withMessage('Term must be between 1 and 120 months'),
  body('monthlyIncome')
    .isNumeric()
    .withMessage('Monthly income must be a number')
    .custom((value) => {
      if (value <= 0) {
        throw new Error('Monthly income must be greater than 0');
      }
      return true;
    }),
  body('existingDebts')
    .optional()
    .isNumeric()
    .withMessage('Existing debts must be a number')
    .custom((value) => {
      if (value < 0) {
        throw new Error('Existing debts cannot be negative');
      }
      return true;
    }),
  handleValidationErrors
];

// Public settings routes (available to all authenticated users)
router.get('/', loanSettingsController.getLoanSettings);
router.get('/eligibility-criteria', loanSettingsController.getEligibilityCriteria);
router.post('/check-eligibility', validateEligibilityCheck, loanSettingsController.checkEligibility);
router.get('/interest-rate', loanSettingsController.getInterestRate);

// Admin-only routes (commented out for demo - would require admin role)
// router.patch('/', requireRole(['admin']), validateLoanSettings, loanSettingsController.updateLoanSettings);

export default router;
