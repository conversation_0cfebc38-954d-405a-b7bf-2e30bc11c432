import { Router } from 'express';
import { loanController } from '../controllers/loanController';
import { authenticateToken } from '../middleware/auth';
import { 
  validateLoanApplication,
  validateLoanUpdate,
  validateUUIDParam,
  validatePaginationQuery,
  handleValidationErrors 
} from '../middleware/validation';
import { body } from 'express-validator';

const router = Router();

// All loan routes require authentication
router.use(authenticateToken);

// Validation rules
const validateLoanCalculation = [
  body('amount')
    .isNumeric()
    .withMessage('Amount must be a number')
    .custom((value) => {
      if (value <= 0) {
        throw new Error('Amount must be greater than 0');
      }
      return true;
    }),
  body('termMonths')
    .isInt({ min: 1, max: 120 })
    .withMessage('Term must be between 1 and 120 months'),
  body('interestRate')
    .optional()
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Interest rate must be between 0.1% and 100%'),
  handleValidationErrors
];

// Loan application routes
router.post('/applications', validateLoanApplication, loanController.createLoanApplication);
router.get('/applications', validatePaginationQuery, handleValidationErrors, loanController.getLoanApplications);
router.get('/applications/:applicationId', 
  validateUUIDParam('applicationId'), 
  handleValidationErrors, 
  loanController.getLoanApplication
);
router.patch('/applications/:applicationId/cancel', 
  validateUUIDParam('applicationId'), 
  handleValidationErrors, 
  loanController.cancelLoanApplication
);

// Loan management routes
router.get('/', validatePaginationQuery, handleValidationErrors, loanController.getLoans);
router.get('/summary', loanController.getLoanSummary);
router.get('/:loanId', 
  validateUUIDParam('loanId'), 
  handleValidationErrors, 
  loanController.getLoan
);
router.patch('/:loanId', 
  validateUUIDParam('loanId'), 
  validateLoanUpdate, 
  loanController.updateLoan
);

// Loan calculation
router.post('/calculate', validateLoanCalculation, loanController.calculateLoan);

export default router;
