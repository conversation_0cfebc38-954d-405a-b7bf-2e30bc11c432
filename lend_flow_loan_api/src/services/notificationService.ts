import { v4 as uuidv4 } from 'uuid';
import { database } from '../config/database';
import { 
  Notification, 
  Loan, 
  Payment, 
  LoanApplication 
} from '../types';

export class NotificationService {
  // Send payment confirmation notification
  async sendPaymentConfirmation(userId: string, payment: Payment, loan: Loan): Promise<void> {
    const notification: Notification = {
      id: uuidv4(),
      userId,
      title: 'Payment Received',
      message: `Your payment of $${payment.amount.toFixed(2)} for loan #${loan.id.slice(-6)} has been processed successfully.`,
      type: 'payment_received',
      isRead: false,
      metadata: {
        paymentId: payment.id,
        loanId: loan.id,
        amount: payment.amount
      },
      createdAt: new Date()
    };

    database.createNotification(notification);
  }

  // Send payment reminder notification
  async sendPaymentReminder(userId: string, loan: Loan, reminderType: 'upcoming' | 'overdue'): Promise<void> {
    let title: string;
    let message: string;
    let notificationType: 'payment_reminder' | 'payment_overdue';

    if (reminderType === 'upcoming') {
      title = 'Payment Reminder';
      message = `Your loan payment of $${loan.monthlyPayment.toFixed(2)} is due on ${loan.nextPaymentDate?.toISOString().split('T')[0]}. Don't forget to make your payment on time.`;
      notificationType = 'payment_reminder';
    } else {
      title = 'Payment Overdue';
      message = `Your loan payment of $${loan.monthlyPayment.toFixed(2)} was due on ${loan.nextPaymentDate?.toISOString().split('T')[0]} and is now overdue. Please make your payment as soon as possible to avoid additional fees.`;
      notificationType = 'payment_overdue';
    }

    const notification: Notification = {
      id: uuidv4(),
      userId,
      title,
      message,
      type: notificationType,
      isRead: false,
      metadata: {
        loanId: loan.id,
        amount: loan.monthlyPayment,
        dueDate: loan.nextPaymentDate
      },
      createdAt: new Date()
    };

    database.createNotification(notification);
  }

  // Send loan approval notification
  async sendLoanApprovalNotification(userId: string, application: LoanApplication): Promise<void> {
    const notification: Notification = {
      id: uuidv4(),
      userId,
      title: 'Loan Approved! 🎉',
      message: `Congratulations! Your loan application for $${application.approvedAmount?.toFixed(2)} has been approved. The funds will be disbursed to your account shortly.`,
      type: 'loan_approved',
      isRead: false,
      metadata: {
        applicationId: application.id,
        approvedAmount: application.approvedAmount,
        interestRate: application.approvedRate
      },
      createdAt: new Date()
    };

    database.createNotification(notification);
  }

  // Send loan rejection notification
  async sendLoanRejectionNotification(userId: string, application: LoanApplication): Promise<void> {
    const notification: Notification = {
      id: uuidv4(),
      userId,
      title: 'Loan Application Update',
      message: `We regret to inform you that your loan application for $${application.requestedAmount.toFixed(2)} has been declined. ${application.rejectionReason || 'Please contact us for more information.'}`,
      type: 'loan_rejected',
      isRead: false,
      metadata: {
        applicationId: application.id,
        requestedAmount: application.requestedAmount,
        rejectionReason: application.rejectionReason
      },
      createdAt: new Date()
    };

    database.createNotification(notification);
  }

  // Send loan completion notification
  async sendLoanCompletionNotification(userId: string, loan: Loan): Promise<void> {
    const notification: Notification = {
      id: uuidv4(),
      userId,
      title: 'Loan Completed! 🎊',
      message: `Congratulations! You have successfully completed your loan #${loan.id.slice(-6)}. Thank you for being a valued customer.`,
      type: 'general',
      isRead: false,
      metadata: {
        loanId: loan.id,
        originalAmount: loan.amount,
        totalPaid: loan.amount
      },
      createdAt: new Date()
    };

    database.createNotification(notification);
  }

  // Send loan disbursement notification
  async sendLoanDisbursementNotification(userId: string, loan: Loan): Promise<void> {
    const notification: Notification = {
      id: uuidv4(),
      userId,
      title: 'Loan Disbursed',
      message: `Your loan of $${loan.amount.toFixed(2)} has been disbursed to your account. Your first payment of $${loan.monthlyPayment.toFixed(2)} is due on ${loan.nextPaymentDate?.toISOString().split('T')[0]}.`,
      type: 'loan_disbursed',
      isRead: false,
      metadata: {
        loanId: loan.id,
        amount: loan.amount,
        firstPaymentDate: loan.nextPaymentDate
      },
      createdAt: new Date()
    };

    database.createNotification(notification);
  }

  // Send application status update notification
  async sendApplicationStatusUpdate(userId: string, application: LoanApplication): Promise<void> {
    let title: string;
    let message: string;

    switch (application.status) {
      case 'under_review':
        title = 'Application Under Review';
        message = `Your loan application for $${application.requestedAmount.toFixed(2)} is currently under review. We'll notify you once a decision has been made.`;
        break;
      case 'credit_check':
        title = 'Credit Check in Progress';
        message = `We're currently performing a credit check for your loan application. This process typically takes 1-2 business days.`;
        break;
      default:
        title = 'Application Status Update';
        message = `Your loan application status has been updated to: ${application.status}`;
    }

    const notification: Notification = {
      id: uuidv4(),
      userId,
      title,
      message,
      type: 'application_update',
      isRead: false,
      metadata: {
        applicationId: application.id,
        status: application.status,
        requestedAmount: application.requestedAmount
      },
      createdAt: new Date()
    };

    database.createNotification(notification);
  }

  // Send system maintenance notification
  async sendSystemMaintenanceNotification(userId: string, maintenanceInfo: {
    startTime: Date;
    endTime: Date;
    description: string;
  }): Promise<void> {
    const notification: Notification = {
      id: uuidv4(),
      userId,
      title: 'Scheduled Maintenance',
      message: `Our system will be undergoing maintenance from ${maintenanceInfo.startTime.toLocaleString()} to ${maintenanceInfo.endTime.toLocaleString()}. ${maintenanceInfo.description}`,
      type: 'system_maintenance',
      isRead: false,
      metadata: {
        startTime: maintenanceInfo.startTime,
        endTime: maintenanceInfo.endTime,
        description: maintenanceInfo.description
      },
      scheduledFor: maintenanceInfo.startTime,
      createdAt: new Date()
    };

    database.createNotification(notification);
  }

  // Mark notification as read
  async markAsRead(notificationId: string, userId: string): Promise<boolean> {
    const notification = database.getNotificationsByUserId(userId)
      .find(n => n.id === notificationId);

    if (!notification) {
      return false;
    }

    database.updateNotification(notificationId, { isRead: true });
    return true;
  }

  // Mark all notifications as read for a user
  async markAllAsRead(userId: string): Promise<number> {
    const notifications = database.getNotificationsByUserId(userId)
      .filter(n => !n.isRead);

    notifications.forEach(notification => {
      database.updateNotification(notification.id, { isRead: true });
    });

    return notifications.length;
  }

  // Get unread notification count
  getUnreadCount(userId: string): number {
    return database.getNotificationsByUserId(userId)
      .filter(n => !n.isRead).length;
  }

  // Delete old notifications (cleanup job)
  async cleanupOldNotifications(daysOld: number = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    // In a real database, this would be a DELETE query
    // For our in-memory database, we'll just track how many would be deleted
    const allUsers = database.getAllUsers();
    let deletedCount = 0;

    allUsers.forEach(user => {
      const notifications = database.getNotificationsByUserId(user.id);
      const oldNotifications = notifications.filter(n => 
        n.createdAt < cutoffDate && n.isRead
      );
      deletedCount += oldNotifications.length;
    });

    return deletedCount;
  }

  // Send bulk notifications (for promotional messages, etc.)
  async sendBulkNotification(userIds: string[], notification: {
    title: string;
    message: string;
    type: 'general';
    metadata?: Record<string, any>;
  }): Promise<number> {
    let sentCount = 0;

    for (const userId of userIds) {
      const userNotification: Notification = {
        id: uuidv4(),
        userId,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        isRead: false,
        metadata: notification.metadata,
        createdAt: new Date()
      };

      database.createNotification(userNotification);
      sentCount++;
    }

    return sentCount;
  }

  // Get notification preferences (placeholder for future implementation)
  async getNotificationPreferences(userId: string): Promise<{
    email: boolean;
    sms: boolean;
    push: boolean;
    paymentReminders: boolean;
    loanUpdates: boolean;
    promotional: boolean;
  }> {
    // Default preferences - in real app, this would come from database
    return {
      email: true,
      sms: true,
      push: true,
      paymentReminders: true,
      loanUpdates: true,
      promotional: false
    };
  }

  // Update notification preferences (placeholder for future implementation)
  async updateNotificationPreferences(userId: string, preferences: {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
    paymentReminders?: boolean;
    loanUpdates?: boolean;
    promotional?: boolean;
  }): Promise<boolean> {
    // In real app, this would update the database
    console.log(`Updated notification preferences for user ${userId}:`, preferences);
    return true;
  }
}

export const notificationService = new NotificationService();
