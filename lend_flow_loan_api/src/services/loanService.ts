import { v4 as uuidv4 } from 'uuid';
import { addMonths, format } from 'date-fns';
import { database } from '../config/database';
import { 
  Loan, 
  LoanApplication, 
  PaymentSchedule, 
  RiskAssessment,
  LoanSettings
} from '../types';

export class LoanService {
  // Calculate loan details including monthly payment and schedule
  calculateLoanDetails(principal: number, annualInterestRate: number, termMonths: number) {
    const monthlyRate = annualInterestRate / 100 / 12;
    let monthlyPayment: number;

    if (monthlyRate === 0) {
      monthlyPayment = principal / termMonths;
    } else {
      const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
      const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
      monthlyPayment = principal * (numerator / denominator);
    }

    const totalAmount = monthlyPayment * termMonths;
    const totalInterest = totalAmount - principal;

    // Generate payment schedule
    const paymentSchedule = this.generatePaymentSchedule(
      principal,
      annualInterestRate,
      termMonths,
      new Date()
    );

    return {
      principal: Math.round(principal * 100) / 100,
      interestRate: annualInterestRate,
      termMonths,
      monthlyPayment: Math.round(monthlyPayment * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      totalAmount: Math.round(totalAmount * 100) / 100,
      paymentSchedule
    };
  }

  // Generate payment schedule for a loan
  generatePaymentSchedule(
    principal: number,
    annualInterestRate: number,
    termMonths: number,
    startDate: Date
  ) {
    const monthlyRate = annualInterestRate / 100 / 12;
    const monthlyPayment = this.calculateMonthlyPayment(principal, annualInterestRate, termMonths);
    const schedule = [];
    
    let remainingBalance = principal;
    
    for (let i = 1; i <= termMonths; i++) {
      const interestAmount = remainingBalance * monthlyRate;
      const principalAmount = monthlyPayment - interestAmount;
      remainingBalance = Math.max(0, remainingBalance - principalAmount);
      
      const paymentDate = addMonths(startDate, i);
      
      schedule.push({
        paymentNumber: i,
        paymentDate: format(paymentDate, 'yyyy-MM-dd'),
        paymentAmount: Math.round(monthlyPayment * 100) / 100,
        principalAmount: Math.round(principalAmount * 100) / 100,
        interestAmount: Math.round(interestAmount * 100) / 100,
        remainingBalance: Math.round(remainingBalance * 100) / 100,
      });
    }
    
    return schedule;
  }

  // Calculate monthly payment
  private calculateMonthlyPayment(principal: number, annualInterestRate: number, termMonths: number): number {
    if (annualInterestRate === 0) {
      return principal / termMonths;
    }
    
    const monthlyRate = annualInterestRate / 100 / 12;
    const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
    const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
    
    return principal * (numerator / denominator);
  }

  // Perform risk assessment for loan application
  async performRiskAssessment(application: LoanApplication): Promise<RiskAssessment> {
    const user = database.getUserById(application.userId);
    
    if (!user) {
      throw new Error('User not found');
    }

    // Calculate debt-to-income ratio
    const debtToIncomeRatio = (application.existingDebts + this.calculateMonthlyPayment(
      application.requestedAmount,
      12, // Default rate for calculation
      application.termMonths
    )) / application.monthlyIncome;

    // Calculate employment stability score (0-100)
    const employmentStability = this.calculateEmploymentStability(application.employmentInfo);

    // Get credit score (default to 650 if not available)
    const creditScore = user.creditScore || 650;

    // Calculate payment history score (mock - in real app, get from credit bureau)
    const paymentHistory = this.calculatePaymentHistoryScore(user.id);

    // Calculate overall risk score
    const overallRiskScore = this.calculateOverallRiskScore({
      creditScore,
      debtToIncomeRatio,
      employmentStability,
      paymentHistory
    });

    // Determine risk level
    let riskLevel: 'low' | 'medium' | 'high';
    if (overallRiskScore >= 80) riskLevel = 'low';
    else if (overallRiskScore >= 60) riskLevel = 'medium';
    else riskLevel = 'high';

    // Generate recommendations
    const recommendations = this.generateRecommendations(overallRiskScore, debtToIncomeRatio, creditScore);

    const riskAssessment: RiskAssessment = {
      id: uuidv4(),
      applicationId: application.id,
      creditScore,
      debtToIncomeRatio: Math.round(debtToIncomeRatio * 100) / 100,
      employmentStability,
      paymentHistory,
      overallRiskScore,
      riskLevel,
      recommendations,
      createdAt: new Date()
    };

    return riskAssessment;
  }

  // Process loan application (approve/reject based on risk assessment)
  async processApplication(applicationId: string): Promise<LoanApplication> {
    const application = database.getLoanApplicationById(applicationId);
    
    if (!application) {
      throw new Error('Application not found');
    }

    const riskAssessment = await this.performRiskAssessment(application);
    const settings = database.getLoanSettings();

    if (!settings) {
      throw new Error('Loan settings not configured');
    }

    // Auto-approval logic
    if (riskAssessment.overallRiskScore >= 70 && 
        riskAssessment.debtToIncomeRatio <= settings.maxDebtToIncomeRatio &&
        riskAssessment.creditScore >= settings.minCreditScore) {
      
      // Approve application
      const updatedApplication = database.updateLoanApplication(applicationId, {
        status: 'approved',
        approvedAmount: application.requestedAmount,
        approvedRate: this.calculateInterestRate(riskAssessment),
        riskAssessment
      });

      // Create loan
      if (updatedApplication) {
        await this.createLoanFromApplication(updatedApplication);
      }

      return updatedApplication!;
    } else {
      // Reject application
      const rejectionReason = this.generateRejectionReason(riskAssessment, settings);
      
      return database.updateLoanApplication(applicationId, {
        status: 'rejected',
        rejectionReason,
        riskAssessment
      })!;
    }
  }

  // Create loan from approved application
  private async createLoanFromApplication(application: LoanApplication): Promise<Loan> {
    const loanCalculation = this.calculateLoanDetails(
      application.approvedAmount!,
      application.approvedRate!,
      application.termMonths
    );

    const loan: Loan = {
      id: uuidv4(),
      userId: application.userId,
      applicationId: application.id,
      amount: application.approvedAmount!,
      interestRate: application.approvedRate!,
      termMonths: application.termMonths,
      monthlyPayment: loanCalculation.monthlyPayment,
      totalAmount: loanCalculation.totalAmount,
      status: 'active',
      purpose: application.purpose,
      applicationDate: application.createdAt,
      approvalDate: new Date(),
      disbursementDate: new Date(),
      maturityDate: addMonths(new Date(), application.termMonths),
      nextPaymentDate: addMonths(new Date(), 1),
      remainingBalance: application.approvedAmount!,
      paymentsCompleted: 0,
      totalPayments: application.termMonths,
      latePaymentCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const createdLoan = database.createLoan(loan);

    // Create payment schedule
    await this.createPaymentSchedule(createdLoan);

    return createdLoan;
  }

  // Create payment schedule for loan
  private async createPaymentSchedule(loan: Loan): Promise<void> {
    const schedule = this.generatePaymentSchedule(
      loan.amount,
      loan.interestRate,
      loan.termMonths,
      loan.disbursementDate || new Date()
    );

    schedule.forEach((payment, index) => {
      const paymentSchedule: PaymentSchedule = {
        id: uuidv4(),
        loanId: loan.id,
        paymentNumber: payment.paymentNumber,
        dueDate: new Date(payment.paymentDate),
        amount: payment.paymentAmount,
        principalAmount: payment.principalAmount,
        interestAmount: payment.interestAmount,
        remainingBalance: payment.remainingBalance,
        status: 'upcoming',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      database.createPaymentSchedule(paymentSchedule);
    });
  }

  // Calculate employment stability score
  private calculateEmploymentStability(employmentInfo: any): number {
    let score = 50; // Base score

    // Employment type scoring
    switch (employmentInfo.employmentType) {
      case 'full_time':
        score += 30;
        break;
      case 'part_time':
        score += 15;
        break;
      case 'self_employed':
        score += 10;
        break;
      case 'contract':
        score += 5;
        break;
      case 'unemployed':
        score = 0;
        break;
    }

    // Years employed scoring
    if (employmentInfo.yearsEmployed >= 5) score += 20;
    else if (employmentInfo.yearsEmployed >= 2) score += 15;
    else if (employmentInfo.yearsEmployed >= 1) score += 10;
    else score += 5;

    return Math.min(100, score);
  }

  // Calculate payment history score (mock implementation)
  private calculatePaymentHistoryScore(userId: string): number {
    const userLoans = database.getLoansByUserId(userId);
    
    if (userLoans.length === 0) return 70; // Default for new customers

    const totalLoans = userLoans.length;
    const completedLoans = userLoans.filter(loan => loan.status === 'completed').length;
    const defaultedLoans = userLoans.filter(loan => loan.status === 'defaulted').length;

    let score = 50;
    score += (completedLoans / totalLoans) * 40;
    score -= (defaultedLoans / totalLoans) * 30;

    return Math.max(0, Math.min(100, score));
  }

  // Calculate overall risk score
  private calculateOverallRiskScore(factors: {
    creditScore: number;
    debtToIncomeRatio: number;
    employmentStability: number;
    paymentHistory: number;
  }): number {
    const creditWeight = 0.4;
    const dtiWeight = 0.3;
    const employmentWeight = 0.2;
    const historyWeight = 0.1;

    // Normalize credit score (300-850 to 0-100)
    const normalizedCredit = Math.max(0, Math.min(100, (factors.creditScore - 300) / 5.5));

    // Normalize DTI (lower is better, 0-1 to 100-0)
    const normalizedDTI = Math.max(0, 100 - (factors.debtToIncomeRatio * 100));

    const score = (
      normalizedCredit * creditWeight +
      normalizedDTI * dtiWeight +
      factors.employmentStability * employmentWeight +
      factors.paymentHistory * historyWeight
    );

    return Math.round(score);
  }

  // Calculate interest rate based on risk assessment
  private calculateInterestRate(riskAssessment: RiskAssessment): number {
    const settings = database.getLoanSettings()!;
    let rate = settings.baseInterestRate;

    // Adjust rate based on risk level
    switch (riskAssessment.riskLevel) {
      case 'low':
        rate = settings.baseInterestRate;
        break;
      case 'medium':
        rate = settings.baseInterestRate + 3;
        break;
      case 'high':
        rate = settings.baseInterestRate + 6;
        break;
    }

    return Math.min(rate, settings.maxInterestRate);
  }

  // Generate recommendations based on risk assessment
  private generateRecommendations(riskScore: number, dtiRatio: number, creditScore: number): string[] {
    const recommendations: string[] = [];

    if (riskScore < 60) {
      recommendations.push('Consider improving credit score before reapplying');
    }

    if (dtiRatio > 0.4) {
      recommendations.push('Reduce existing debt to improve debt-to-income ratio');
    }

    if (creditScore < 600) {
      recommendations.push('Work on building credit history');
    }

    if (recommendations.length === 0) {
      recommendations.push('Application meets all criteria for approval');
    }

    return recommendations;
  }

  // Generate rejection reason
  private generateRejectionReason(riskAssessment: RiskAssessment, settings: LoanSettings): string {
    const reasons: string[] = [];

    if (riskAssessment.creditScore < settings.minCreditScore) {
      reasons.push(`Credit score below minimum requirement (${settings.minCreditScore})`);
    }

    if (riskAssessment.debtToIncomeRatio > settings.maxDebtToIncomeRatio) {
      reasons.push(`Debt-to-income ratio exceeds maximum allowed (${settings.maxDebtToIncomeRatio * 100}%)`);
    }

    if (riskAssessment.overallRiskScore < 50) {
      reasons.push('Overall risk assessment indicates high default probability');
    }

    return reasons.join('; ') || 'Application does not meet current lending criteria';
  }
}

export const loanService = new LoanService();
