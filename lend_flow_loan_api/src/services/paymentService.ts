import { v4 as uuidv4 } from 'uuid';
import { addMonths, differenceInDays } from 'date-fns';
import { database } from '../config/database';
import { notificationService } from './notificationService';
import { 
  Payment, 
  PaymentSchedule, 
  Loan,
  CreatePaymentRequest
} from '../types';

export class PaymentService {
  // Process a payment
  async processPayment(paymentData: CreatePaymentRequest, userId: string): Promise<Payment> {
    const loan = database.getLoanById(paymentData.loanId);
    
    if (!loan) {
      throw new Error('Loan not found');
    }

    if (loan.userId !== userId) {
      throw new Error('Access denied');
    }

    if (loan.status !== 'active') {
      throw new Error('Cannot make payment on inactive loan');
    }

    if (paymentData.amount <= 0) {
      throw new Error('Payment amount must be greater than 0');
    }

    if (paymentData.amount > loan.remainingBalance) {
      throw new Error('Payment amount cannot exceed remaining balance');
    }

    // Calculate payment breakdown
    const paymentBreakdown = this.calculatePaymentBreakdown(loan, paymentData.amount);

    // Create payment record
    const payment: Payment = {
      id: uuidv4(),
      loanId: loan.id,
      amount: paymentData.amount,
      principalAmount: paymentBreakdown.principalAmount,
      interestAmount: paymentBreakdown.interestAmount,
      lateFee: paymentBreakdown.lateFee,
      paymentDate: new Date(),
      dueDate: loan.nextPaymentDate || new Date(),
      status: 'completed', // In real app, this would be 'pending' until confirmed
      paymentMethod: paymentData.paymentMethod,
      transactionId: uuidv4(), // Mock transaction ID
      reference: paymentData.reference,
      notes: paymentData.notes,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const createdPayment = database.createPayment(payment);

    // Update loan balance and payment count
    const newRemainingBalance = loan.remainingBalance - paymentBreakdown.principalAmount;
    const newPaymentsCompleted = loan.paymentsCompleted + 1;

    let newLoanStatus: any = loan.status;
    let nextPaymentDate = loan.nextPaymentDate;

    // Check if loan is fully paid
    if (newRemainingBalance <= 0) {
      newLoanStatus = 'completed';
      nextPaymentDate = undefined;
    } else {
      // Calculate next payment date
      nextPaymentDate = addMonths(new Date(), 1);
    }

    // Update loan
    database.updateLoan(loan.id, {
      remainingBalance: Math.max(0, newRemainingBalance),
      paymentsCompleted: newPaymentsCompleted,
      status: newLoanStatus,
      nextPaymentDate,
      updatedAt: new Date()
    });

    // Update payment schedule
    await this.updatePaymentSchedule(loan.id, paymentData.amount);

    // Send notification
    await notificationService.sendPaymentConfirmation(userId, createdPayment, loan);

    // If loan is completed, send completion notification
    if (newLoanStatus === 'completed') {
      await notificationService.sendLoanCompletionNotification(userId, loan);
    }

    return createdPayment;
  }

  // Calculate payment breakdown (principal, interest, late fees)
  private calculatePaymentBreakdown(loan: Loan, paymentAmount: number) {
    const monthlyInterestRate = loan.interestRate / 100 / 12;
    const interestAmount = loan.remainingBalance * monthlyInterestRate;
    
    // Calculate late fee if payment is overdue
    let lateFee = 0;
    if (loan.nextPaymentDate && new Date() > new Date(loan.nextPaymentDate)) {
      const daysLate = differenceInDays(new Date(), new Date(loan.nextPaymentDate));
      const settings = database.getLoanSettings();
      
      if (settings && daysLate > settings.gracePeriodDays) {
        lateFee = Math.min(
          settings.lateFeeFixedAmount,
          loan.monthlyPayment * (settings.lateFeePercentage / 100)
        );
      }
    }

    // Allocate payment: late fee first, then interest, then principal
    let remainingPayment = paymentAmount;
    
    const actualLateFee = Math.min(lateFee, remainingPayment);
    remainingPayment -= actualLateFee;
    
    const actualInterestAmount = Math.min(interestAmount, remainingPayment);
    remainingPayment -= actualInterestAmount;
    
    const principalAmount = remainingPayment;

    return {
      principalAmount: Math.round(principalAmount * 100) / 100,
      interestAmount: Math.round(actualInterestAmount * 100) / 100,
      lateFee: Math.round(actualLateFee * 100) / 100
    };
  }

  // Update payment schedule after payment
  private async updatePaymentSchedule(loanId: string, paymentAmount: number): Promise<void> {
    const schedules = database.getPaymentSchedulesByLoanId(loanId);
    
    // Find the next unpaid schedule item
    const nextSchedule = schedules.find(schedule => 
      schedule.status === 'upcoming' || schedule.status === 'due' || schedule.status === 'overdue'
    );

    if (nextSchedule) {
      let scheduleStatus: 'paid' | 'partial' = 'paid';
      
      if (paymentAmount < nextSchedule.amount) {
        scheduleStatus = 'partial';
      }

      database.updatePaymentSchedule(nextSchedule.id, {
        status: scheduleStatus,
        paidDate: new Date(),
        paidAmount: paymentAmount
      });
    }
  }

  // Calculate early payoff amount
  calculateEarlyPayoff(loan: Loan) {
    const currentDate = new Date();
    const remainingBalance = loan.remainingBalance;
    
    // In a real implementation, you might apply early payoff discounts
    // For now, we'll use the remaining balance as the payoff amount
    const payoffAmount = remainingBalance;
    
    // Calculate interest savings
    const remainingPayments = loan.totalPayments - loan.paymentsCompleted;
    const totalRemainingPayments = loan.monthlyPayment * remainingPayments;
    const interestSavings = totalRemainingPayments - remainingBalance;

    return {
      payoffAmount: Math.round(payoffAmount * 100) / 100,
      interestSavings: Math.round(Math.max(0, interestSavings) * 100) / 100,
      payoffDate: currentDate.toISOString().split('T')[0],
      remainingPayments,
      monthsSaved: remainingPayments
    };
  }

  // Generate payment reminders
  async generatePaymentReminders(): Promise<void> {
    const allLoans = database.getAllLoans();
    const activeLoans = allLoans.filter(loan => loan.status === 'active');

    for (const loan of activeLoans) {
      if (loan.nextPaymentDate) {
        const daysUntilDue = differenceInDays(new Date(loan.nextPaymentDate), new Date());
        
        // Send reminder 3 days before due date
        if (daysUntilDue === 3) {
          await notificationService.sendPaymentReminder(loan.userId, loan, 'upcoming');
        }
        
        // Send overdue notice 1 day after due date
        if (daysUntilDue === -1) {
          await notificationService.sendPaymentReminder(loan.userId, loan, 'overdue');
          
          // Update loan late payment count
          database.updateLoan(loan.id, {
            latePaymentCount: loan.latePaymentCount + 1
          });
        }
      }
    }
  }

  // Get payment statistics for a user
  getPaymentStatistics(userId: string) {
    const payments = database.getPaymentsByUserId(userId);
    const loans = database.getLoansByUserId(userId);

    const totalPaid = payments
      .filter(p => p.status === 'completed')
      .reduce((sum, payment) => sum + payment.amount, 0);

    const totalInterestPaid = payments
      .filter(p => p.status === 'completed')
      .reduce((sum, payment) => sum + payment.interestAmount, 0);

    const totalLateFees = payments
      .filter(p => p.status === 'completed')
      .reduce((sum, payment) => sum + (payment.lateFee || 0), 0);

    const onTimePayments = payments.filter(payment => {
      return payment.status === 'completed' && 
             new Date(payment.paymentDate) <= new Date(payment.dueDate);
    }).length;

    const latePayments = payments.filter(payment => {
      return payment.status === 'completed' && 
             new Date(payment.paymentDate) > new Date(payment.dueDate);
    }).length;

    const paymentHistory = payments.length > 0 ? (onTimePayments / payments.length) * 100 : 0;

    return {
      totalPayments: payments.length,
      totalPaid: Math.round(totalPaid * 100) / 100,
      totalInterestPaid: Math.round(totalInterestPaid * 100) / 100,
      totalLateFees: Math.round(totalLateFees * 100) / 100,
      onTimePayments,
      latePayments,
      paymentHistoryScore: Math.round(paymentHistory),
      averagePaymentAmount: payments.length > 0 ? 
        Math.round((totalPaid / payments.length) * 100) / 100 : 0
    };
  }

  // Validate payment amount
  validatePaymentAmount(loan: Loan, amount: number): { isValid: boolean; error?: string } {
    if (amount <= 0) {
      return { isValid: false, error: 'Payment amount must be greater than 0' };
    }

    if (amount > loan.remainingBalance) {
      return { isValid: false, error: 'Payment amount cannot exceed remaining balance' };
    }

    // Minimum payment validation (at least interest amount)
    const monthlyInterestRate = loan.interestRate / 100 / 12;
    const minimumPayment = loan.remainingBalance * monthlyInterestRate;

    if (amount < minimumPayment) {
      return { 
        isValid: false, 
        error: `Minimum payment amount is $${minimumPayment.toFixed(2)}` 
      };
    }

    return { isValid: true };
  }
}

export const paymentService = new PaymentService();
