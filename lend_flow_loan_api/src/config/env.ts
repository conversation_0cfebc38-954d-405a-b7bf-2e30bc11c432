import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  // Server Configuration
  PORT: process.env.PORT || 3000,
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // JWT Configuration
  JWT_SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
  
  // Database Configuration (for future use)
  DATABASE_URL: process.env.DATABASE_URL || '',
  
  // API Configuration
  API_VERSION: process.env.API_VERSION || 'v1',
  API_PREFIX: process.env.API_PREFIX || '/api',
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  
  // CORS Configuration
  CORS_ORIGIN: process.env.CORS_ORIGIN || '*',
  
  // File Upload Configuration
  MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB
  UPLOAD_PATH: process.env.UPLOAD_PATH || './uploads',
  
  // Notification Configuration
  ENABLE_SMS: process.env.ENABLE_SMS === 'true',
  ENABLE_EMAIL: process.env.ENABLE_EMAIL === 'true',
  
  // SMS Configuration (for future integration)
  SMS_API_KEY: process.env.SMS_API_KEY || '',
  SMS_API_URL: process.env.SMS_API_URL || '',
  
  // Email Configuration (for future integration)
  EMAIL_HOST: process.env.EMAIL_HOST || '',
  EMAIL_PORT: parseInt(process.env.EMAIL_PORT || '587'),
  EMAIL_USER: process.env.EMAIL_USER || '',
  EMAIL_PASS: process.env.EMAIL_PASS || '',
  
  // Payment Gateway Configuration (for future integration)
  PAYMENT_GATEWAY_URL: process.env.PAYMENT_GATEWAY_URL || '',
  PAYMENT_GATEWAY_KEY: process.env.PAYMENT_GATEWAY_KEY || '',
  PAYMENT_GATEWAY_SECRET: process.env.PAYMENT_GATEWAY_SECRET || '',
  
  // Credit Check Service Configuration (for future integration)
  CREDIT_CHECK_API_URL: process.env.CREDIT_CHECK_API_URL || '',
  CREDIT_CHECK_API_KEY: process.env.CREDIT_CHECK_API_KEY || '',
  
  // Logging Configuration
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  LOG_FILE: process.env.LOG_FILE || './logs/app.log',
  
  // Security Configuration
  BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || '12'),
  
  // Business Rules Configuration
  DEFAULT_INTEREST_RATE: parseFloat(process.env.DEFAULT_INTEREST_RATE || '12'),
  MAX_LOAN_AMOUNT: parseFloat(process.env.MAX_LOAN_AMOUNT || '100000'),
  MIN_LOAN_AMOUNT: parseFloat(process.env.MIN_LOAN_AMOUNT || '1000'),
  MAX_LOAN_TERM_MONTHS: parseInt(process.env.MAX_LOAN_TERM_MONTHS || '60'),
  MIN_LOAN_TERM_MONTHS: parseInt(process.env.MIN_LOAN_TERM_MONTHS || '6'),
  
  // Feature Flags
  ENABLE_CREDIT_CHECK: process.env.ENABLE_CREDIT_CHECK === 'true',
  ENABLE_RISK_ASSESSMENT: process.env.ENABLE_RISK_ASSESSMENT === 'true',
  ENABLE_AUTO_APPROVAL: process.env.ENABLE_AUTO_APPROVAL === 'true',
  
  // Development Configuration
  ENABLE_SWAGGER: process.env.ENABLE_SWAGGER !== 'false', // Enabled by default
  ENABLE_MORGAN_LOGGING: process.env.ENABLE_MORGAN_LOGGING !== 'false',
};

// Validate required environment variables
export const validateConfig = (): void => {
  const requiredVars = ['JWT_SECRET'];
  
  for (const varName of requiredVars) {
    if (!process.env[varName] && config.NODE_ENV === 'production') {
      throw new Error(`Required environment variable ${varName} is not set`);
    }
  }
  
  // Validate numeric values
  if (isNaN(config.PORT as number)) {
    throw new Error('PORT must be a valid number');
  }
  
  if (isNaN(config.RATE_LIMIT_WINDOW_MS)) {
    throw new Error('RATE_LIMIT_WINDOW_MS must be a valid number');
  }
  
  if (isNaN(config.RATE_LIMIT_MAX_REQUESTS)) {
    throw new Error('RATE_LIMIT_MAX_REQUESTS must be a valid number');
  }
};

// Environment helper functions
export const isDevelopment = (): boolean => config.NODE_ENV === 'development';
export const isProduction = (): boolean => config.NODE_ENV === 'production';
export const isTest = (): boolean => config.NODE_ENV === 'test';
