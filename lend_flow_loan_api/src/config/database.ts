import { 
  User, 
  Loan, 
  LoanApplication, 
  Payment, 
  PaymentSchedule, 
  LoanSettings, 
  Notification,
  RiskAssessment,
  CreditCheckResult
} from '../types';

// In-memory database for demo purposes
// In production, this would be replaced with a real database (PostgreSQL, MongoDB, etc.)

export class InMemoryDatabase {
  private users: Map<string, User> = new Map();
  private loans: Map<string, Loan> = new Map();
  private loanApplications: Map<string, LoanApplication> = new Map();
  private payments: Map<string, Payment> = new Map();
  private paymentSchedules: Map<string, PaymentSchedule> = new Map();
  private loanSettings: Map<string, LoanSettings> = new Map();
  private notifications: Map<string, Notification> = new Map();
  private riskAssessments: Map<string, RiskAssessment> = new Map();
  private creditChecks: Map<string, CreditCheckResult> = new Map();

  constructor() {
    this.initializeDefaultData();
  }

  // User operations
  createUser(user: User): User {
    this.users.set(user.id, user);
    return user;
  }

  getUserById(id: string): User | undefined {
    return this.users.get(id);
  }

  getUserByPhone(phoneNumber: string): User | undefined {
    return Array.from(this.users.values()).find(user => user.phoneNumber === phoneNumber);
  }

  updateUser(id: string, updates: Partial<User>): User | undefined {
    const user = this.users.get(id);
    if (user) {
      const updatedUser = { ...user, ...updates, updatedAt: new Date() };
      this.users.set(id, updatedUser);
      return updatedUser;
    }
    return undefined;
  }

  getAllUsers(): User[] {
    return Array.from(this.users.values());
  }

  // Loan operations
  createLoan(loan: Loan): Loan {
    this.loans.set(loan.id, loan);
    return loan;
  }

  getLoanById(id: string): Loan | undefined {
    return this.loans.get(id);
  }

  getLoansByUserId(userId: string): Loan[] {
    return Array.from(this.loans.values()).filter(loan => loan.userId === userId);
  }

  updateLoan(id: string, updates: Partial<Loan>): Loan | undefined {
    const loan = this.loans.get(id);
    if (loan) {
      const updatedLoan = { ...loan, ...updates, updatedAt: new Date() };
      this.loans.set(id, updatedLoan);
      return updatedLoan;
    }
    return undefined;
  }

  getAllLoans(): Loan[] {
    return Array.from(this.loans.values());
  }

  // Loan Application operations
  createLoanApplication(application: LoanApplication): LoanApplication {
    this.loanApplications.set(application.id, application);
    return application;
  }

  getLoanApplicationById(id: string): LoanApplication | undefined {
    return this.loanApplications.get(id);
  }

  getLoanApplicationsByUserId(userId: string): LoanApplication[] {
    return Array.from(this.loanApplications.values()).filter(app => app.userId === userId);
  }

  updateLoanApplication(id: string, updates: Partial<LoanApplication>): LoanApplication | undefined {
    const application = this.loanApplications.get(id);
    if (application) {
      const updatedApplication = { ...application, ...updates, updatedAt: new Date() };
      this.loanApplications.set(id, updatedApplication);
      return updatedApplication;
    }
    return undefined;
  }

  // Payment operations
  createPayment(payment: Payment): Payment {
    this.payments.set(payment.id, payment);
    return payment;
  }

  getPaymentById(id: string): Payment | undefined {
    return this.payments.get(id);
  }

  getPaymentsByLoanId(loanId: string): Payment[] {
    return Array.from(this.payments.values()).filter(payment => payment.loanId === loanId);
  }

  getPaymentsByUserId(userId: string): Payment[] {
    const userLoans = this.getLoansByUserId(userId);
    const loanIds = userLoans.map(loan => loan.id);
    return Array.from(this.payments.values()).filter(payment => loanIds.includes(payment.loanId));
  }

  // Payment Schedule operations
  createPaymentSchedule(schedule: PaymentSchedule): PaymentSchedule {
    this.paymentSchedules.set(schedule.id, schedule);
    return schedule;
  }

  getPaymentSchedulesByLoanId(loanId: string): PaymentSchedule[] {
    return Array.from(this.paymentSchedules.values())
      .filter(schedule => schedule.loanId === loanId)
      .sort((a, b) => a.paymentNumber - b.paymentNumber);
  }

  updatePaymentSchedule(id: string, updates: Partial<PaymentSchedule>): PaymentSchedule | undefined {
    const schedule = this.paymentSchedules.get(id);
    if (schedule) {
      const updatedSchedule = { ...schedule, ...updates, updatedAt: new Date() };
      this.paymentSchedules.set(id, updatedSchedule);
      return updatedSchedule;
    }
    return undefined;
  }

  // Loan Settings operations
  getLoanSettings(): LoanSettings | undefined {
    const activeSettings = Array.from(this.loanSettings.values()).find(settings => settings.isActive);
    return activeSettings;
  }

  updateLoanSettings(id: string, updates: Partial<LoanSettings>): LoanSettings | undefined {
    const settings = this.loanSettings.get(id);
    if (settings) {
      const updatedSettings = { ...settings, ...updates, updatedAt: new Date() };
      this.loanSettings.set(id, updatedSettings);
      return updatedSettings;
    }
    return undefined;
  }

  // Notification operations
  createNotification(notification: Notification): Notification {
    this.notifications.set(notification.id, notification);
    return notification;
  }

  getNotificationsByUserId(userId: string): Notification[] {
    return Array.from(this.notifications.values())
      .filter(notification => notification.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  updateNotification(id: string, updates: Partial<Notification>): Notification | undefined {
    const notification = this.notifications.get(id);
    if (notification) {
      const updatedNotification = { ...notification, ...updates };
      this.notifications.set(id, updatedNotification);
      return updatedNotification;
    }
    return undefined;
  }

  private initializeDefaultData(): void {
    // Initialize default loan settings
    const defaultSettings: LoanSettings = {
      id: 'default-settings',
      minLoanAmount: 1000,
      maxLoanAmount: 100000,
      minTermMonths: 6,
      maxTermMonths: 60,
      baseInterestRate: 12,
      maxInterestRate: 30,
      lateFeePercentage: 5,
      lateFeeFixedAmount: 50,
      gracePeriodDays: 7,
      maxDebtToIncomeRatio: 0.4,
      minCreditScore: 300,
      processingFeePercentage: 2,
      processingFeeFixedAmount: 100,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.loanSettings.set(defaultSettings.id, defaultSettings);

    // Initialize demo user
    const demoUser: User = {
      id: 'user-**********',
      phoneNumber: '**********',
      firstName: 'Demo',
      lastName: 'User',
      email: '<EMAIL>',
      address: '123 Demo Street, Demo City',
      creditScore: 720,
      isActive: true,
      isVerified: true,
      bankingInfo: {
        accountNumber: '**********',
        bankName: 'Demo Bank',
        accountType: 'savings',
        accountHolderName: 'Demo User'
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date()
    };

    this.users.set(demoUser.id, demoUser);
  }
}

// Singleton instance
export const database = new InMemoryDatabase();
