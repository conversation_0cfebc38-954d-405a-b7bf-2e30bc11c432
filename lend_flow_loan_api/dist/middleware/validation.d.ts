import { Request, Response, NextFunction } from 'express';
export declare const handleValidationErrors: (req: Request, res: Response, next: NextFunction) => void;
export declare const validatePhoneNumber: import("express-validator").ValidationChain[];
export declare const validateUserProfile: import("express-validator").ValidationChain[];
export declare const validateLoanApplication: import("express-validator").ValidationChain[];
export declare const validatePayment: import("express-validator").ValidationChain[];
export declare const validateLoanUpdate: import("express-validator").ValidationChain[];
export declare const validateUUIDParam: (paramName: string) => import("express-validator").ValidationChain[];
export declare const validatePaginationQuery: import("express-validator").ValidationChain[];
export declare const validateLoanSettings: import("express-validator").ValidationChain[];
//# sourceMappingURL=validation.d.ts.map