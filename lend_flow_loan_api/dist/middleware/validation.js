"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateLoanSettings = exports.validatePaginationQuery = exports.validateUUIDParam = exports.validateLoanUpdate = exports.validatePayment = exports.validateLoanApplication = exports.validateUserProfile = exports.validatePhoneNumber = exports.handleValidationErrors = void 0;
const express_validator_1 = require("express-validator");
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        const validationErrors = errors.array().map(error => ({
            field: error.type === 'field' ? error.path : 'unknown',
            message: error.msg,
            value: error.type === 'field' ? error.value : undefined
        }));
        res.status(400).json({
            success: false,
            error: 'Validation failed',
            errors: validationErrors
        });
        return;
    }
    next();
};
exports.handleValidationErrors = handleValidationErrors;
exports.validatePhoneNumber = [
    (0, express_validator_1.body)('phoneNumber')
        .isMobilePhone('any')
        .withMessage('Please provide a valid phone number')
        .isLength({ min: 10, max: 15 })
        .withMessage('Phone number must be between 10 and 15 digits')
];
exports.validateUserProfile = [
    (0, express_validator_1.body)('firstName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('First name can only contain letters and spaces'),
    (0, express_validator_1.body)('lastName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('Last name can only contain letters and spaces'),
    (0, express_validator_1.body)('email')
        .optional()
        .isEmail()
        .withMessage('Please provide a valid email address'),
    (0, express_validator_1.body)('dateOfBirth')
        .optional()
        .isISO8601()
        .withMessage('Please provide a valid date of birth in ISO format'),
    (0, express_validator_1.body)('bankingInfo.accountNumber')
        .optional()
        .isLength({ min: 8, max: 20 })
        .withMessage('Account number must be between 8 and 20 characters'),
    (0, express_validator_1.body)('bankingInfo.bankName')
        .optional()
        .isLength({ min: 2, max: 100 })
        .withMessage('Bank name must be between 2 and 100 characters'),
    (0, express_validator_1.body)('bankingInfo.accountType')
        .optional()
        .isIn(['savings', 'checking'])
        .withMessage('Account type must be either savings or checking')
];
exports.validateLoanApplication = [
    (0, express_validator_1.body)('requestedAmount')
        .isNumeric()
        .withMessage('Requested amount must be a number')
        .custom((value) => {
        if (value < 1000 || value > 100000) {
            throw new Error('Requested amount must be between $1,000 and $100,000');
        }
        return true;
    }),
    (0, express_validator_1.body)('purpose')
        .isLength({ min: 5, max: 200 })
        .withMessage('Purpose must be between 5 and 200 characters'),
    (0, express_validator_1.body)('termMonths')
        .isInt({ min: 6, max: 60 })
        .withMessage('Term must be between 6 and 60 months'),
    (0, express_validator_1.body)('monthlyIncome')
        .isNumeric()
        .withMessage('Monthly income must be a number')
        .custom((value) => {
        if (value < 1000) {
            throw new Error('Monthly income must be at least $1,000');
        }
        return true;
    }),
    (0, express_validator_1.body)('existingDebts')
        .isNumeric()
        .withMessage('Existing debts must be a number')
        .custom((value) => {
        if (value < 0) {
            throw new Error('Existing debts cannot be negative');
        }
        return true;
    }),
    (0, express_validator_1.body)('employmentInfo.employer')
        .isLength({ min: 2, max: 100 })
        .withMessage('Employer name must be between 2 and 100 characters'),
    (0, express_validator_1.body)('employmentInfo.position')
        .isLength({ min: 2, max: 100 })
        .withMessage('Position must be between 2 and 100 characters'),
    (0, express_validator_1.body)('employmentInfo.employmentType')
        .isIn(['full_time', 'part_time', 'self_employed', 'unemployed', 'contract'])
        .withMessage('Invalid employment type'),
    (0, express_validator_1.body)('employmentInfo.yearsEmployed')
        .isNumeric()
        .withMessage('Years employed must be a number')
        .custom((value) => {
        if (value < 0 || value > 50) {
            throw new Error('Years employed must be between 0 and 50');
        }
        return true;
    })
];
exports.validatePayment = [
    (0, express_validator_1.body)('loanId')
        .isUUID()
        .withMessage('Invalid loan ID format'),
    (0, express_validator_1.body)('amount')
        .isNumeric()
        .withMessage('Amount must be a number')
        .custom((value) => {
        if (value <= 0) {
            throw new Error('Amount must be greater than 0');
        }
        return true;
    }),
    (0, express_validator_1.body)('paymentMethod')
        .isIn(['bank_transfer', 'card', 'mobile_money', 'cash', 'check'])
        .withMessage('Invalid payment method'),
    (0, express_validator_1.body)('reference')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Reference must not exceed 100 characters')
];
exports.validateLoanUpdate = [
    (0, express_validator_1.body)('status')
        .optional()
        .isIn(['draft', 'pending', 'under_review', 'approved', 'rejected', 'active', 'completed', 'defaulted', 'cancelled'])
        .withMessage('Invalid loan status'),
    (0, express_validator_1.body)('notes')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Notes must not exceed 500 characters'),
    (0, express_validator_1.body)('nextPaymentDate')
        .optional()
        .isISO8601()
        .withMessage('Next payment date must be a valid ISO date')
];
const validateUUIDParam = (paramName) => [
    (0, express_validator_1.param)(paramName)
        .isUUID()
        .withMessage(`Invalid ${paramName} format`)
];
exports.validateUUIDParam = validateUUIDParam;
exports.validatePaginationQuery = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    (0, express_validator_1.query)('sortBy')
        .optional()
        .isLength({ min: 1, max: 50 })
        .withMessage('Sort field must be between 1 and 50 characters'),
    (0, express_validator_1.query)('sortOrder')
        .optional()
        .isIn(['asc', 'desc'])
        .withMessage('Sort order must be either asc or desc')
];
exports.validateLoanSettings = [
    (0, express_validator_1.body)('minLoanAmount')
        .isNumeric()
        .withMessage('Minimum loan amount must be a number')
        .custom((value) => {
        if (value < 100) {
            throw new Error('Minimum loan amount must be at least $100');
        }
        return true;
    }),
    (0, express_validator_1.body)('maxLoanAmount')
        .isNumeric()
        .withMessage('Maximum loan amount must be a number')
        .custom((value, { req }) => {
        if (value <= req.body.minLoanAmount) {
            throw new Error('Maximum loan amount must be greater than minimum loan amount');
        }
        return true;
    }),
    (0, express_validator_1.body)('minTermMonths')
        .isInt({ min: 1, max: 12 })
        .withMessage('Minimum term must be between 1 and 12 months'),
    (0, express_validator_1.body)('maxTermMonths')
        .isInt({ min: 12, max: 120 })
        .withMessage('Maximum term must be between 12 and 120 months')
        .custom((value, { req }) => {
        if (value <= req.body.minTermMonths) {
            throw new Error('Maximum term must be greater than minimum term');
        }
        return true;
    }),
    (0, express_validator_1.body)('baseInterestRate')
        .isFloat({ min: 0.1, max: 50 })
        .withMessage('Base interest rate must be between 0.1% and 50%'),
    (0, express_validator_1.body)('maxInterestRate')
        .isFloat({ min: 0.1, max: 100 })
        .withMessage('Maximum interest rate must be between 0.1% and 100%')
        .custom((value, { req }) => {
        if (value <= req.body.baseInterestRate) {
            throw new Error('Maximum interest rate must be greater than base interest rate');
        }
        return true;
    })
];
//# sourceMappingURL=validation.js.map