"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = __importDefault(require("./app"));
const env_1 = require("./config/env");
try {
    (0, env_1.validateConfig)();
}
catch (error) {
    console.error('Configuration validation failed:', error);
    process.exit(1);
}
const PORT = env_1.config.PORT;
app_1.default.listen(PORT, () => {
    console.log(`
🚀 Lend Flow API Server Started Successfully!

📍 Server Details:
   • Port: ${PORT}
   • Environment: ${env_1.config.NODE_ENV}
   • API Version: ${env_1.config.API_VERSION}
   • Base URL: http://localhost:${PORT}${env_1.config.API_PREFIX}/${env_1.config.API_VERSION}

📚 API Documentation:
   • Docs: http://localhost:${PORT}${env_1.config.API_PREFIX}/${env_1.config.API_VERSION}/docs
   • Health: http://localhost:${PORT}/health

🔐 Demo Credentials:
   • Phone: **********
   • PIN: 9999

🛠️  Available Endpoints:
   • POST /api/v1/auth/login - Login
   • GET /api/v1/loans - Get loans
   • GET /api/v1/loans/summary - Loan summary
   • POST /api/v1/loans/calculate - Calculate loan
   • GET /api/v1/payments - Get payments
   • GET /api/v1/profile - Get profile
   • GET /api/v1/loan-settings - Get settings

⚡ Ready to handle requests!
  `);
});
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    process.exit(0);
});
process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    process.exit(0);
});
exports.default = app_1.default;
//# sourceMappingURL=index.js.map