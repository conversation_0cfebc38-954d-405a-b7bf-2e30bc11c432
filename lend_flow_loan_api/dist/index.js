"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const env_1 = require("./config/env");
const auth_1 = __importDefault(require("./routes/auth"));
const loans_1 = __importDefault(require("./routes/loans"));
const payments_1 = __importDefault(require("./routes/payments"));
const profile_1 = __importDefault(require("./routes/profile"));
const loanSettings_1 = __importDefault(require("./routes/loanSettings"));
try {
    (0, env_1.validateConfig)();
}
catch (error) {
    console.error('Configuration validation failed:', error);
    process.exit(1);
}
const app = (0, express_1.default)();
app.use((0, helmet_1.default)({
    contentSecurityPolicy: (0, env_1.isDevelopment)() ? false : undefined,
}));
app.use((0, cors_1.default)({
    origin: env_1.config.CORS_ORIGIN,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: env_1.config.RATE_LIMIT_WINDOW_MS,
    max: env_1.config.RATE_LIMIT_MAX_REQUESTS,
    message: {
        success: false,
        error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use((0, compression_1.default)());
if (env_1.config.ENABLE_MORGAN_LOGGING) {
    app.use((0, morgan_1.default)((0, env_1.isDevelopment)() ? 'dev' : 'combined'));
}
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Lend Flow API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: env_1.config.NODE_ENV,
    });
});
const apiPrefix = `${env_1.config.API_PREFIX}/${env_1.config.API_VERSION}`;
app.use(`${apiPrefix}/auth`, auth_1.default);
app.use(`${apiPrefix}/loans`, loans_1.default);
app.use(`${apiPrefix}/payments`, payments_1.default);
app.use(`${apiPrefix}/profile`, profile_1.default);
app.use(`${apiPrefix}/loan-settings`, loanSettings_1.default);
app.get(`${apiPrefix}/docs`, (req, res) => {
    res.json({
        success: true,
        message: 'Lend Flow API Documentation',
        version: '1.0.0',
        endpoints: {
            auth: {
                'POST /auth/login': 'Login with phone number and PIN',
                'POST /auth/send-otp': 'Send OTP for phone verification',
                'POST /auth/verify-otp': 'Verify OTP',
                'POST /auth/reset-pin': 'Reset PIN with OTP',
                'GET /auth/me': 'Get current user info',
                'POST /auth/change-pin': 'Change PIN',
                'POST /auth/refresh-token': 'Refresh JWT token',
                'POST /auth/logout': 'Logout',
            },
            loans: {
                'POST /loans/applications': 'Create loan application',
                'GET /loans/applications': 'Get user loan applications',
                'GET /loans/applications/:id': 'Get specific loan application',
                'PATCH /loans/applications/:id/cancel': 'Cancel loan application',
                'GET /loans': 'Get user loans',
                'GET /loans/summary': 'Get loan summary for dashboard',
                'GET /loans/:id': 'Get specific loan',
                'PATCH /loans/:id': 'Update loan (admin)',
                'POST /loans/calculate': 'Calculate loan details',
            },
            payments: {
                'POST /payments': 'Create payment',
                'GET /payments': 'Get user payments',
                'GET /payments/summary': 'Get payment summary',
                'GET /payments/:id': 'Get specific payment',
                'GET /payments/loans/:loanId': 'Get loan payments',
                'GET /payments/loans/:loanId/schedule': 'Get payment schedule',
                'GET /payments/loans/:loanId/early-payoff': 'Calculate early payoff',
            },
            profile: {
                'GET /profile': 'Get user profile',
                'PATCH /profile': 'Update user profile',
                'DELETE /profile': 'Delete user account',
                'GET /profile/financial-summary': 'Get financial summary',
                'GET /profile/verification-status': 'Get verification status',
                'GET /profile/notifications': 'Get notifications',
                'PATCH /profile/notifications/:id/read': 'Mark notification as read',
                'PATCH /profile/notifications/read-all': 'Mark all notifications as read',
                'GET /profile/notification-preferences': 'Get notification preferences',
                'PATCH /profile/notification-preferences': 'Update notification preferences',
            },
            loanSettings: {
                'GET /loan-settings': 'Get loan settings',
                'GET /loan-settings/eligibility-criteria': 'Get eligibility criteria',
                'POST /loan-settings/check-eligibility': 'Check loan eligibility',
                'GET /loan-settings/interest-rate': 'Get interest rate for user',
            },
        },
        authentication: {
            type: 'Bearer Token',
            header: 'Authorization: Bearer <token>',
            note: 'Most endpoints require authentication. Get token from /auth/login',
        },
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        message: `The requested endpoint ${req.method} ${req.originalUrl} was not found.`,
    });
});
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);
    if (error.name === 'ValidationError') {
        res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: error.message,
        });
        return;
    }
    if (error.name === 'UnauthorizedError') {
        res.status(401).json({
            success: false,
            error: 'Unauthorized',
            message: 'Invalid or expired token',
        });
        return;
    }
    res.status(error.status || 500).json({
        success: false,
        error: (0, env_1.isDevelopment)() ? error.message : 'Internal server error',
        ...((0, env_1.isDevelopment)() && { stack: error.stack }),
    });
});
const PORT = env_1.config.PORT;
app.listen(PORT, () => {
    console.log(`
🚀 Lend Flow API Server Started Successfully!

📍 Server Details:
   • Port: ${PORT}
   • Environment: ${env_1.config.NODE_ENV}
   • API Version: ${env_1.config.API_VERSION}
   • Base URL: http://localhost:${PORT}${apiPrefix}

📚 API Documentation:
   • Docs: http://localhost:${PORT}${apiPrefix}/docs
   • Health: http://localhost:${PORT}/health

🔐 Authentication:
   • Demo Login: POST ${apiPrefix}/auth/login
   • Phone: **********
   • PIN: 9999

🛠️  Available Endpoints:
   • Auth: ${apiPrefix}/auth/*
   • Loans: ${apiPrefix}/loans/*
   • Payments: ${apiPrefix}/payments/*
   • Profile: ${apiPrefix}/profile/*
   • Settings: ${apiPrefix}/loan-settings/*

⚡ Ready to handle requests!
  `);
});
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    process.exit(0);
});
process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    process.exit(0);
});
exports.default = app;
//# sourceMappingURL=index.js.map