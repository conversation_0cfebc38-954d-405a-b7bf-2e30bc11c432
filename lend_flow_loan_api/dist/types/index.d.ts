export interface User {
    id: string;
    phoneNumber: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    address?: string;
    dateOfBirth?: string;
    nationalId?: string;
    bankingInfo?: BankingInfo;
    creditScore?: number;
    isActive: boolean;
    isVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface BankingInfo {
    accountNumber: string;
    bankName: string;
    accountType: 'savings' | 'checking';
    routingNumber?: string;
    accountHolderName: string;
}
export interface Loan {
    id: string;
    userId: string;
    applicationId: string;
    amount: number;
    interestRate: number;
    termMonths: number;
    monthlyPayment: number;
    totalAmount: number;
    status: LoanStatus;
    purpose: string;
    applicationDate: Date;
    approvalDate?: Date;
    disbursementDate?: Date;
    maturityDate?: Date;
    nextPaymentDate?: Date;
    remainingBalance: number;
    paymentsCompleted: number;
    totalPayments: number;
    latePaymentCount: number;
    collateral?: string;
    guarantor?: string;
    notes?: string;
    createdAt: Date;
    updatedAt: Date;
}
export type LoanStatus = 'draft' | 'pending' | 'under_review' | 'approved' | 'rejected' | 'active' | 'completed' | 'defaulted' | 'cancelled';
export interface LoanApplication {
    id: string;
    userId: string;
    requestedAmount: number;
    purpose: string;
    termMonths: number;
    employmentInfo: EmploymentInfo;
    monthlyIncome: number;
    existingDebts: number;
    status: ApplicationStatus;
    documents: Document[];
    creditCheckResult?: CreditCheckResult;
    riskAssessment?: RiskAssessment;
    approvedAmount?: number;
    approvedRate?: number;
    rejectionReason?: string;
    reviewNotes?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface EmploymentInfo {
    employer: string;
    position: string;
    employmentType: 'full_time' | 'part_time' | 'self_employed' | 'unemployed' | 'contract';
    monthlyIncome: number;
    yearsEmployed: number;
    employerContact?: string;
    workAddress?: string;
}
export interface Document {
    id: string;
    type: DocumentType;
    url: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    isVerified: boolean;
    uploadedAt: Date;
}
export type DocumentType = 'id_document' | 'proof_of_income' | 'bank_statement' | 'employment_letter' | 'utility_bill' | 'tax_return' | 'collateral_document';
export type ApplicationStatus = 'draft' | 'submitted' | 'under_review' | 'credit_check' | 'approved' | 'rejected' | 'cancelled';
export interface Payment {
    id: string;
    loanId: string;
    scheduleId?: string;
    amount: number;
    principalAmount: number;
    interestAmount: number;
    lateFee?: number;
    paymentDate: Date;
    dueDate: Date;
    status: PaymentStatus;
    paymentMethod: PaymentMethod;
    transactionId?: string;
    reference?: string;
    notes?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface PaymentSchedule {
    id: string;
    loanId: string;
    paymentNumber: number;
    dueDate: Date;
    amount: number;
    principalAmount: number;
    interestAmount: number;
    remainingBalance: number;
    status: ScheduleStatus;
    paidDate?: Date;
    paidAmount?: number;
    createdAt: Date;
    updatedAt: Date;
}
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'late' | 'partial';
export type ScheduleStatus = 'upcoming' | 'due' | 'overdue' | 'paid' | 'partial';
export type PaymentMethod = 'bank_transfer' | 'card' | 'mobile_money' | 'cash' | 'check';
export interface LoanSettings {
    id: string;
    minLoanAmount: number;
    maxLoanAmount: number;
    minTermMonths: number;
    maxTermMonths: number;
    baseInterestRate: number;
    maxInterestRate: number;
    lateFeePercentage: number;
    lateFeeFixedAmount: number;
    gracePeriodDays: number;
    maxDebtToIncomeRatio: number;
    minCreditScore: number;
    processingFeePercentage: number;
    processingFeeFixedAmount: number;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface RiskAssessment {
    id: string;
    applicationId: string;
    creditScore: number;
    debtToIncomeRatio: number;
    employmentStability: number;
    paymentHistory: number;
    overallRiskScore: number;
    riskLevel: 'low' | 'medium' | 'high';
    recommendations: string[];
    createdAt: Date;
}
export interface CreditCheckResult {
    id: string;
    userId: string;
    creditScore: number;
    creditHistory: string;
    outstandingDebts: number;
    defaultHistory: boolean;
    checkDate: Date;
    provider: string;
}
export interface Notification {
    id: string;
    userId: string;
    title: string;
    message: string;
    type: NotificationType;
    isRead: boolean;
    actionUrl?: string;
    metadata?: Record<string, any>;
    scheduledFor?: Date;
    sentAt?: Date;
    createdAt: Date;
}
export type NotificationType = 'payment_reminder' | 'payment_overdue' | 'loan_approved' | 'loan_rejected' | 'payment_received' | 'loan_disbursed' | 'application_update' | 'system_maintenance' | 'general';
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
    errors?: ValidationError[];
    pagination?: PaginationInfo;
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}
export interface PaginationInfo {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
export interface CreateLoanApplicationRequest {
    requestedAmount: number;
    purpose: string;
    termMonths: number;
    employmentInfo: EmploymentInfo;
    monthlyIncome: number;
    existingDebts: number;
}
export interface UpdateLoanRequest {
    status?: LoanStatus;
    notes?: string;
    nextPaymentDate?: Date;
}
export interface CreatePaymentRequest {
    loanId: string;
    amount: number;
    paymentMethod: PaymentMethod;
    reference?: string;
    notes?: string;
}
export interface UpdateProfileRequest {
    firstName?: string;
    lastName?: string;
    email?: string;
    address?: string;
    dateOfBirth?: string;
    bankingInfo?: BankingInfo;
}
export interface AuthUser {
    id: string;
    phoneNumber: string;
    role: 'user' | 'admin' | 'agent';
    isActive: boolean;
}
export interface JWTPayload {
    userId: string;
    phoneNumber: string;
    role: string;
    iat: number;
    exp: number;
}
//# sourceMappingURL=index.d.ts.map