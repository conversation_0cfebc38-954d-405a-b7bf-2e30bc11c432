import { Loan, Payment, LoanApplication } from '../types';
export declare class NotificationService {
    sendPaymentConfirmation(userId: string, payment: Payment, loan: Loan): Promise<void>;
    sendPaymentReminder(userId: string, loan: Loan, reminderType: 'upcoming' | 'overdue'): Promise<void>;
    sendLoanApprovalNotification(userId: string, application: LoanApplication): Promise<void>;
    sendLoanRejectionNotification(userId: string, application: LoanApplication): Promise<void>;
    sendLoanCompletionNotification(userId: string, loan: Loan): Promise<void>;
    sendLoanDisbursementNotification(userId: string, loan: Loan): Promise<void>;
    sendApplicationStatusUpdate(userId: string, application: LoanApplication): Promise<void>;
    sendSystemMaintenanceNotification(userId: string, maintenanceInfo: {
        startTime: Date;
        endTime: Date;
        description: string;
    }): Promise<void>;
    markAsRead(notificationId: string, userId: string): Promise<boolean>;
    markAllAsRead(userId: string): Promise<number>;
    getUnreadCount(userId: string): number;
    cleanupOldNotifications(daysOld?: number): Promise<number>;
    sendBulkNotification(userIds: string[], notification: {
        title: string;
        message: string;
        type: 'general';
        metadata?: Record<string, any>;
    }): Promise<number>;
    getNotificationPreferences(userId: string): Promise<{
        email: boolean;
        sms: boolean;
        push: boolean;
        paymentReminders: boolean;
        loanUpdates: boolean;
        promotional: boolean;
    }>;
    updateNotificationPreferences(userId: string, preferences: {
        email?: boolean;
        sms?: boolean;
        push?: boolean;
        paymentReminders?: boolean;
        loanUpdates?: boolean;
        promotional?: boolean;
    }): Promise<boolean>;
}
export declare const notificationService: NotificationService;
//# sourceMappingURL=notificationService.d.ts.map