"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationService = exports.NotificationService = void 0;
const uuid_1 = require("uuid");
const database_1 = require("../config/database");
class NotificationService {
    async sendPaymentConfirmation(userId, payment, loan) {
        const notification = {
            id: (0, uuid_1.v4)(),
            userId,
            title: 'Payment Received',
            message: `Your payment of $${payment.amount.toFixed(2)} for loan #${loan.id.slice(-6)} has been processed successfully.`,
            type: 'payment_received',
            isRead: false,
            metadata: {
                paymentId: payment.id,
                loanId: loan.id,
                amount: payment.amount
            },
            createdAt: new Date()
        };
        database_1.database.createNotification(notification);
    }
    async sendPaymentReminder(userId, loan, reminderType) {
        let title;
        let message;
        let notificationType;
        if (reminderType === 'upcoming') {
            title = 'Payment Reminder';
            message = `Your loan payment of $${loan.monthlyPayment.toFixed(2)} is due on ${loan.nextPaymentDate?.toISOString().split('T')[0]}. Don't forget to make your payment on time.`;
            notificationType = 'payment_reminder';
        }
        else {
            title = 'Payment Overdue';
            message = `Your loan payment of $${loan.monthlyPayment.toFixed(2)} was due on ${loan.nextPaymentDate?.toISOString().split('T')[0]} and is now overdue. Please make your payment as soon as possible to avoid additional fees.`;
            notificationType = 'payment_overdue';
        }
        const notification = {
            id: (0, uuid_1.v4)(),
            userId,
            title,
            message,
            type: notificationType,
            isRead: false,
            metadata: {
                loanId: loan.id,
                amount: loan.monthlyPayment,
                dueDate: loan.nextPaymentDate
            },
            createdAt: new Date()
        };
        database_1.database.createNotification(notification);
    }
    async sendLoanApprovalNotification(userId, application) {
        const notification = {
            id: (0, uuid_1.v4)(),
            userId,
            title: 'Loan Approved! 🎉',
            message: `Congratulations! Your loan application for $${application.approvedAmount?.toFixed(2)} has been approved. The funds will be disbursed to your account shortly.`,
            type: 'loan_approved',
            isRead: false,
            metadata: {
                applicationId: application.id,
                approvedAmount: application.approvedAmount,
                interestRate: application.approvedRate
            },
            createdAt: new Date()
        };
        database_1.database.createNotification(notification);
    }
    async sendLoanRejectionNotification(userId, application) {
        const notification = {
            id: (0, uuid_1.v4)(),
            userId,
            title: 'Loan Application Update',
            message: `We regret to inform you that your loan application for $${application.requestedAmount.toFixed(2)} has been declined. ${application.rejectionReason || 'Please contact us for more information.'}`,
            type: 'loan_rejected',
            isRead: false,
            metadata: {
                applicationId: application.id,
                requestedAmount: application.requestedAmount,
                rejectionReason: application.rejectionReason
            },
            createdAt: new Date()
        };
        database_1.database.createNotification(notification);
    }
    async sendLoanCompletionNotification(userId, loan) {
        const notification = {
            id: (0, uuid_1.v4)(),
            userId,
            title: 'Loan Completed! 🎊',
            message: `Congratulations! You have successfully completed your loan #${loan.id.slice(-6)}. Thank you for being a valued customer.`,
            type: 'general',
            isRead: false,
            metadata: {
                loanId: loan.id,
                originalAmount: loan.amount,
                totalPaid: loan.amount
            },
            createdAt: new Date()
        };
        database_1.database.createNotification(notification);
    }
    async sendLoanDisbursementNotification(userId, loan) {
        const notification = {
            id: (0, uuid_1.v4)(),
            userId,
            title: 'Loan Disbursed',
            message: `Your loan of $${loan.amount.toFixed(2)} has been disbursed to your account. Your first payment of $${loan.monthlyPayment.toFixed(2)} is due on ${loan.nextPaymentDate?.toISOString().split('T')[0]}.`,
            type: 'loan_disbursed',
            isRead: false,
            metadata: {
                loanId: loan.id,
                amount: loan.amount,
                firstPaymentDate: loan.nextPaymentDate
            },
            createdAt: new Date()
        };
        database_1.database.createNotification(notification);
    }
    async sendApplicationStatusUpdate(userId, application) {
        let title;
        let message;
        switch (application.status) {
            case 'under_review':
                title = 'Application Under Review';
                message = `Your loan application for $${application.requestedAmount.toFixed(2)} is currently under review. We'll notify you once a decision has been made.`;
                break;
            case 'credit_check':
                title = 'Credit Check in Progress';
                message = `We're currently performing a credit check for your loan application. This process typically takes 1-2 business days.`;
                break;
            default:
                title = 'Application Status Update';
                message = `Your loan application status has been updated to: ${application.status}`;
        }
        const notification = {
            id: (0, uuid_1.v4)(),
            userId,
            title,
            message,
            type: 'application_update',
            isRead: false,
            metadata: {
                applicationId: application.id,
                status: application.status,
                requestedAmount: application.requestedAmount
            },
            createdAt: new Date()
        };
        database_1.database.createNotification(notification);
    }
    async sendSystemMaintenanceNotification(userId, maintenanceInfo) {
        const notification = {
            id: (0, uuid_1.v4)(),
            userId,
            title: 'Scheduled Maintenance',
            message: `Our system will be undergoing maintenance from ${maintenanceInfo.startTime.toLocaleString()} to ${maintenanceInfo.endTime.toLocaleString()}. ${maintenanceInfo.description}`,
            type: 'system_maintenance',
            isRead: false,
            metadata: {
                startTime: maintenanceInfo.startTime,
                endTime: maintenanceInfo.endTime,
                description: maintenanceInfo.description
            },
            scheduledFor: maintenanceInfo.startTime,
            createdAt: new Date()
        };
        database_1.database.createNotification(notification);
    }
    async markAsRead(notificationId, userId) {
        const notification = database_1.database.getNotificationsByUserId(userId)
            .find(n => n.id === notificationId);
        if (!notification) {
            return false;
        }
        database_1.database.updateNotification(notificationId, { isRead: true });
        return true;
    }
    async markAllAsRead(userId) {
        const notifications = database_1.database.getNotificationsByUserId(userId)
            .filter(n => !n.isRead);
        notifications.forEach(notification => {
            database_1.database.updateNotification(notification.id, { isRead: true });
        });
        return notifications.length;
    }
    getUnreadCount(userId) {
        return database_1.database.getNotificationsByUserId(userId)
            .filter(n => !n.isRead).length;
    }
    async cleanupOldNotifications(daysOld = 90) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);
        const allUsers = database_1.database.getAllUsers();
        let deletedCount = 0;
        allUsers.forEach(user => {
            const notifications = database_1.database.getNotificationsByUserId(user.id);
            const oldNotifications = notifications.filter(n => n.createdAt < cutoffDate && n.isRead);
            deletedCount += oldNotifications.length;
        });
        return deletedCount;
    }
    async sendBulkNotification(userIds, notification) {
        let sentCount = 0;
        for (const userId of userIds) {
            const userNotification = {
                id: (0, uuid_1.v4)(),
                userId,
                title: notification.title,
                message: notification.message,
                type: notification.type,
                isRead: false,
                metadata: notification.metadata,
                createdAt: new Date()
            };
            database_1.database.createNotification(userNotification);
            sentCount++;
        }
        return sentCount;
    }
    async getNotificationPreferences(userId) {
        return {
            email: true,
            sms: true,
            push: true,
            paymentReminders: true,
            loanUpdates: true,
            promotional: false
        };
    }
    async updateNotificationPreferences(userId, preferences) {
        console.log(`Updated notification preferences for user ${userId}:`, preferences);
        return true;
    }
}
exports.NotificationService = NotificationService;
exports.notificationService = new NotificationService();
//# sourceMappingURL=notificationService.js.map