import { LoanApplication, RiskAssessment } from '../types';
export declare class LoanService {
    calculateLoanDetails(principal: number, annualInterestRate: number, termMonths: number): {
        principal: number;
        interestRate: number;
        termMonths: number;
        monthlyPayment: number;
        totalInterest: number;
        totalAmount: number;
        paymentSchedule: {
            paymentNumber: number;
            paymentDate: string;
            paymentAmount: number;
            principalAmount: number;
            interestAmount: number;
            remainingBalance: number;
        }[];
    };
    generatePaymentSchedule(principal: number, annualInterestRate: number, termMonths: number, startDate: Date): {
        paymentNumber: number;
        paymentDate: string;
        paymentAmount: number;
        principalAmount: number;
        interestAmount: number;
        remainingBalance: number;
    }[];
    private calculateMonthlyPayment;
    performRiskAssessment(application: LoanApplication): Promise<RiskAssessment>;
    processApplication(applicationId: string): Promise<LoanApplication>;
    private createLoanFromApplication;
    private createPaymentSchedule;
    private calculateEmploymentStability;
    private calculatePaymentHistoryScore;
    private calculateOverallRiskScore;
    private calculateInterestRate;
    private generateRecommendations;
    private generateRejectionReason;
}
export declare const loanService: LoanService;
//# sourceMappingURL=loanService.d.ts.map