"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentService = exports.PaymentService = void 0;
const uuid_1 = require("uuid");
const date_fns_1 = require("date-fns");
const database_1 = require("../config/database");
const notificationService_1 = require("./notificationService");
class PaymentService {
    async processPayment(paymentData, userId) {
        const loan = database_1.database.getLoanById(paymentData.loanId);
        if (!loan) {
            throw new Error('Loan not found');
        }
        if (loan.userId !== userId) {
            throw new Error('Access denied');
        }
        if (loan.status !== 'active') {
            throw new Error('Cannot make payment on inactive loan');
        }
        if (paymentData.amount <= 0) {
            throw new Error('Payment amount must be greater than 0');
        }
        if (paymentData.amount > loan.remainingBalance) {
            throw new Error('Payment amount cannot exceed remaining balance');
        }
        const paymentBreakdown = this.calculatePaymentBreakdown(loan, paymentData.amount);
        const payment = {
            id: (0, uuid_1.v4)(),
            loanId: loan.id,
            amount: paymentData.amount,
            principalAmount: paymentBreakdown.principalAmount,
            interestAmount: paymentBreakdown.interestAmount,
            lateFee: paymentBreakdown.lateFee,
            paymentDate: new Date(),
            dueDate: loan.nextPaymentDate || new Date(),
            status: 'completed',
            paymentMethod: paymentData.paymentMethod,
            transactionId: (0, uuid_1.v4)(),
            reference: paymentData.reference,
            notes: paymentData.notes,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const createdPayment = database_1.database.createPayment(payment);
        const newRemainingBalance = loan.remainingBalance - paymentBreakdown.principalAmount;
        const newPaymentsCompleted = loan.paymentsCompleted + 1;
        let newLoanStatus = loan.status;
        let nextPaymentDate = loan.nextPaymentDate;
        if (newRemainingBalance <= 0) {
            newLoanStatus = 'completed';
            nextPaymentDate = undefined;
        }
        else {
            nextPaymentDate = (0, date_fns_1.addMonths)(new Date(), 1);
        }
        database_1.database.updateLoan(loan.id, {
            remainingBalance: Math.max(0, newRemainingBalance),
            paymentsCompleted: newPaymentsCompleted,
            status: newLoanStatus,
            nextPaymentDate,
            updatedAt: new Date()
        });
        await this.updatePaymentSchedule(loan.id, paymentData.amount);
        await notificationService_1.notificationService.sendPaymentConfirmation(userId, createdPayment, loan);
        if (newLoanStatus === 'completed') {
            await notificationService_1.notificationService.sendLoanCompletionNotification(userId, loan);
        }
        return createdPayment;
    }
    calculatePaymentBreakdown(loan, paymentAmount) {
        const monthlyInterestRate = loan.interestRate / 100 / 12;
        const interestAmount = loan.remainingBalance * monthlyInterestRate;
        let lateFee = 0;
        if (loan.nextPaymentDate && new Date() > new Date(loan.nextPaymentDate)) {
            const daysLate = (0, date_fns_1.differenceInDays)(new Date(), new Date(loan.nextPaymentDate));
            const settings = database_1.database.getLoanSettings();
            if (settings && daysLate > settings.gracePeriodDays) {
                lateFee = Math.min(settings.lateFeeFixedAmount, loan.monthlyPayment * (settings.lateFeePercentage / 100));
            }
        }
        let remainingPayment = paymentAmount;
        const actualLateFee = Math.min(lateFee, remainingPayment);
        remainingPayment -= actualLateFee;
        const actualInterestAmount = Math.min(interestAmount, remainingPayment);
        remainingPayment -= actualInterestAmount;
        const principalAmount = remainingPayment;
        return {
            principalAmount: Math.round(principalAmount * 100) / 100,
            interestAmount: Math.round(actualInterestAmount * 100) / 100,
            lateFee: Math.round(actualLateFee * 100) / 100
        };
    }
    async updatePaymentSchedule(loanId, paymentAmount) {
        const schedules = database_1.database.getPaymentSchedulesByLoanId(loanId);
        const nextSchedule = schedules.find(schedule => schedule.status === 'upcoming' || schedule.status === 'due' || schedule.status === 'overdue');
        if (nextSchedule) {
            let scheduleStatus = 'paid';
            if (paymentAmount < nextSchedule.amount) {
                scheduleStatus = 'partial';
            }
            database_1.database.updatePaymentSchedule(nextSchedule.id, {
                status: scheduleStatus,
                paidDate: new Date(),
                paidAmount: paymentAmount
            });
        }
    }
    calculateEarlyPayoff(loan) {
        const currentDate = new Date();
        const remainingBalance = loan.remainingBalance;
        const payoffAmount = remainingBalance;
        const remainingPayments = loan.totalPayments - loan.paymentsCompleted;
        const totalRemainingPayments = loan.monthlyPayment * remainingPayments;
        const interestSavings = totalRemainingPayments - remainingBalance;
        return {
            payoffAmount: Math.round(payoffAmount * 100) / 100,
            interestSavings: Math.round(Math.max(0, interestSavings) * 100) / 100,
            payoffDate: currentDate.toISOString().split('T')[0],
            remainingPayments,
            monthsSaved: remainingPayments
        };
    }
    async generatePaymentReminders() {
        const allLoans = database_1.database.getAllLoans();
        const activeLoans = allLoans.filter(loan => loan.status === 'active');
        for (const loan of activeLoans) {
            if (loan.nextPaymentDate) {
                const daysUntilDue = (0, date_fns_1.differenceInDays)(new Date(loan.nextPaymentDate), new Date());
                if (daysUntilDue === 3) {
                    await notificationService_1.notificationService.sendPaymentReminder(loan.userId, loan, 'upcoming');
                }
                if (daysUntilDue === -1) {
                    await notificationService_1.notificationService.sendPaymentReminder(loan.userId, loan, 'overdue');
                    database_1.database.updateLoan(loan.id, {
                        latePaymentCount: loan.latePaymentCount + 1
                    });
                }
            }
        }
    }
    getPaymentStatistics(userId) {
        const payments = database_1.database.getPaymentsByUserId(userId);
        const loans = database_1.database.getLoansByUserId(userId);
        const totalPaid = payments
            .filter(p => p.status === 'completed')
            .reduce((sum, payment) => sum + payment.amount, 0);
        const totalInterestPaid = payments
            .filter(p => p.status === 'completed')
            .reduce((sum, payment) => sum + payment.interestAmount, 0);
        const totalLateFees = payments
            .filter(p => p.status === 'completed')
            .reduce((sum, payment) => sum + (payment.lateFee || 0), 0);
        const onTimePayments = payments.filter(payment => {
            return payment.status === 'completed' &&
                new Date(payment.paymentDate) <= new Date(payment.dueDate);
        }).length;
        const latePayments = payments.filter(payment => {
            return payment.status === 'completed' &&
                new Date(payment.paymentDate) > new Date(payment.dueDate);
        }).length;
        const paymentHistory = payments.length > 0 ? (onTimePayments / payments.length) * 100 : 0;
        return {
            totalPayments: payments.length,
            totalPaid: Math.round(totalPaid * 100) / 100,
            totalInterestPaid: Math.round(totalInterestPaid * 100) / 100,
            totalLateFees: Math.round(totalLateFees * 100) / 100,
            onTimePayments,
            latePayments,
            paymentHistoryScore: Math.round(paymentHistory),
            averagePaymentAmount: payments.length > 0 ?
                Math.round((totalPaid / payments.length) * 100) / 100 : 0
        };
    }
    validatePaymentAmount(loan, amount) {
        if (amount <= 0) {
            return { isValid: false, error: 'Payment amount must be greater than 0' };
        }
        if (amount > loan.remainingBalance) {
            return { isValid: false, error: 'Payment amount cannot exceed remaining balance' };
        }
        const monthlyInterestRate = loan.interestRate / 100 / 12;
        const minimumPayment = loan.remainingBalance * monthlyInterestRate;
        if (amount < minimumPayment) {
            return {
                isValid: false,
                error: `Minimum payment amount is $${minimumPayment.toFixed(2)}`
            };
        }
        return { isValid: true };
    }
}
exports.PaymentService = PaymentService;
exports.paymentService = new PaymentService();
//# sourceMappingURL=paymentService.js.map