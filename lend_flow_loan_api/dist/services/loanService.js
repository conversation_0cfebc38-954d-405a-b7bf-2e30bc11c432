"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loanService = exports.LoanService = void 0;
const uuid_1 = require("uuid");
const date_fns_1 = require("date-fns");
const database_1 = require("../config/database");
class LoanService {
    calculateLoanDetails(principal, annualInterestRate, termMonths) {
        const monthlyRate = annualInterestRate / 100 / 12;
        let monthlyPayment;
        if (monthlyRate === 0) {
            monthlyPayment = principal / termMonths;
        }
        else {
            const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
            const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
            monthlyPayment = principal * (numerator / denominator);
        }
        const totalAmount = monthlyPayment * termMonths;
        const totalInterest = totalAmount - principal;
        const paymentSchedule = this.generatePaymentSchedule(principal, annualInterestRate, termMonths, new Date());
        return {
            principal: Math.round(principal * 100) / 100,
            interestRate: annualInterestRate,
            termMonths,
            monthlyPayment: Math.round(monthlyPayment * 100) / 100,
            totalInterest: Math.round(totalInterest * 100) / 100,
            totalAmount: Math.round(totalAmount * 100) / 100,
            paymentSchedule
        };
    }
    generatePaymentSchedule(principal, annualInterestRate, termMonths, startDate) {
        const monthlyRate = annualInterestRate / 100 / 12;
        const monthlyPayment = this.calculateMonthlyPayment(principal, annualInterestRate, termMonths);
        const schedule = [];
        let remainingBalance = principal;
        for (let i = 1; i <= termMonths; i++) {
            const interestAmount = remainingBalance * monthlyRate;
            const principalAmount = monthlyPayment - interestAmount;
            remainingBalance = Math.max(0, remainingBalance - principalAmount);
            const paymentDate = (0, date_fns_1.addMonths)(startDate, i);
            schedule.push({
                paymentNumber: i,
                paymentDate: (0, date_fns_1.format)(paymentDate, 'yyyy-MM-dd'),
                paymentAmount: Math.round(monthlyPayment * 100) / 100,
                principalAmount: Math.round(principalAmount * 100) / 100,
                interestAmount: Math.round(interestAmount * 100) / 100,
                remainingBalance: Math.round(remainingBalance * 100) / 100,
            });
        }
        return schedule;
    }
    calculateMonthlyPayment(principal, annualInterestRate, termMonths) {
        if (annualInterestRate === 0) {
            return principal / termMonths;
        }
        const monthlyRate = annualInterestRate / 100 / 12;
        const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
        const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
        return principal * (numerator / denominator);
    }
    async performRiskAssessment(application) {
        const user = database_1.database.getUserById(application.userId);
        if (!user) {
            throw new Error('User not found');
        }
        const debtToIncomeRatio = (application.existingDebts + this.calculateMonthlyPayment(application.requestedAmount, 12, application.termMonths)) / application.monthlyIncome;
        const employmentStability = this.calculateEmploymentStability(application.employmentInfo);
        const creditScore = user.creditScore || 650;
        const paymentHistory = this.calculatePaymentHistoryScore(user.id);
        const overallRiskScore = this.calculateOverallRiskScore({
            creditScore,
            debtToIncomeRatio,
            employmentStability,
            paymentHistory
        });
        let riskLevel;
        if (overallRiskScore >= 80)
            riskLevel = 'low';
        else if (overallRiskScore >= 60)
            riskLevel = 'medium';
        else
            riskLevel = 'high';
        const recommendations = this.generateRecommendations(overallRiskScore, debtToIncomeRatio, creditScore);
        const riskAssessment = {
            id: (0, uuid_1.v4)(),
            applicationId: application.id,
            creditScore,
            debtToIncomeRatio: Math.round(debtToIncomeRatio * 100) / 100,
            employmentStability,
            paymentHistory,
            overallRiskScore,
            riskLevel,
            recommendations,
            createdAt: new Date()
        };
        return riskAssessment;
    }
    async processApplication(applicationId) {
        const application = database_1.database.getLoanApplicationById(applicationId);
        if (!application) {
            throw new Error('Application not found');
        }
        const riskAssessment = await this.performRiskAssessment(application);
        const settings = database_1.database.getLoanSettings();
        if (!settings) {
            throw new Error('Loan settings not configured');
        }
        if (riskAssessment.overallRiskScore >= 70 &&
            riskAssessment.debtToIncomeRatio <= settings.maxDebtToIncomeRatio &&
            riskAssessment.creditScore >= settings.minCreditScore) {
            const updatedApplication = database_1.database.updateLoanApplication(applicationId, {
                status: 'approved',
                approvedAmount: application.requestedAmount,
                approvedRate: this.calculateInterestRate(riskAssessment),
                riskAssessment
            });
            if (updatedApplication) {
                await this.createLoanFromApplication(updatedApplication);
            }
            return updatedApplication;
        }
        else {
            const rejectionReason = this.generateRejectionReason(riskAssessment, settings);
            return database_1.database.updateLoanApplication(applicationId, {
                status: 'rejected',
                rejectionReason,
                riskAssessment
            });
        }
    }
    async createLoanFromApplication(application) {
        const loanCalculation = this.calculateLoanDetails(application.approvedAmount, application.approvedRate, application.termMonths);
        const loan = {
            id: (0, uuid_1.v4)(),
            userId: application.userId,
            applicationId: application.id,
            amount: application.approvedAmount,
            interestRate: application.approvedRate,
            termMonths: application.termMonths,
            monthlyPayment: loanCalculation.monthlyPayment,
            totalAmount: loanCalculation.totalAmount,
            status: 'active',
            purpose: application.purpose,
            applicationDate: application.createdAt,
            approvalDate: new Date(),
            disbursementDate: new Date(),
            maturityDate: (0, date_fns_1.addMonths)(new Date(), application.termMonths),
            nextPaymentDate: (0, date_fns_1.addMonths)(new Date(), 1),
            remainingBalance: application.approvedAmount,
            paymentsCompleted: 0,
            totalPayments: application.termMonths,
            latePaymentCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const createdLoan = database_1.database.createLoan(loan);
        await this.createPaymentSchedule(createdLoan);
        return createdLoan;
    }
    async createPaymentSchedule(loan) {
        const schedule = this.generatePaymentSchedule(loan.amount, loan.interestRate, loan.termMonths, loan.disbursementDate || new Date());
        schedule.forEach((payment, index) => {
            const paymentSchedule = {
                id: (0, uuid_1.v4)(),
                loanId: loan.id,
                paymentNumber: payment.paymentNumber,
                dueDate: new Date(payment.paymentDate),
                amount: payment.paymentAmount,
                principalAmount: payment.principalAmount,
                interestAmount: payment.interestAmount,
                remainingBalance: payment.remainingBalance,
                status: 'upcoming',
                createdAt: new Date(),
                updatedAt: new Date()
            };
            database_1.database.createPaymentSchedule(paymentSchedule);
        });
    }
    calculateEmploymentStability(employmentInfo) {
        let score = 50;
        switch (employmentInfo.employmentType) {
            case 'full_time':
                score += 30;
                break;
            case 'part_time':
                score += 15;
                break;
            case 'self_employed':
                score += 10;
                break;
            case 'contract':
                score += 5;
                break;
            case 'unemployed':
                score = 0;
                break;
        }
        if (employmentInfo.yearsEmployed >= 5)
            score += 20;
        else if (employmentInfo.yearsEmployed >= 2)
            score += 15;
        else if (employmentInfo.yearsEmployed >= 1)
            score += 10;
        else
            score += 5;
        return Math.min(100, score);
    }
    calculatePaymentHistoryScore(userId) {
        const userLoans = database_1.database.getLoansByUserId(userId);
        if (userLoans.length === 0)
            return 70;
        const totalLoans = userLoans.length;
        const completedLoans = userLoans.filter(loan => loan.status === 'completed').length;
        const defaultedLoans = userLoans.filter(loan => loan.status === 'defaulted').length;
        let score = 50;
        score += (completedLoans / totalLoans) * 40;
        score -= (defaultedLoans / totalLoans) * 30;
        return Math.max(0, Math.min(100, score));
    }
    calculateOverallRiskScore(factors) {
        const creditWeight = 0.4;
        const dtiWeight = 0.3;
        const employmentWeight = 0.2;
        const historyWeight = 0.1;
        const normalizedCredit = Math.max(0, Math.min(100, (factors.creditScore - 300) / 5.5));
        const normalizedDTI = Math.max(0, 100 - (factors.debtToIncomeRatio * 100));
        const score = (normalizedCredit * creditWeight +
            normalizedDTI * dtiWeight +
            factors.employmentStability * employmentWeight +
            factors.paymentHistory * historyWeight);
        return Math.round(score);
    }
    calculateInterestRate(riskAssessment) {
        const settings = database_1.database.getLoanSettings();
        let rate = settings.baseInterestRate;
        switch (riskAssessment.riskLevel) {
            case 'low':
                rate = settings.baseInterestRate;
                break;
            case 'medium':
                rate = settings.baseInterestRate + 3;
                break;
            case 'high':
                rate = settings.baseInterestRate + 6;
                break;
        }
        return Math.min(rate, settings.maxInterestRate);
    }
    generateRecommendations(riskScore, dtiRatio, creditScore) {
        const recommendations = [];
        if (riskScore < 60) {
            recommendations.push('Consider improving credit score before reapplying');
        }
        if (dtiRatio > 0.4) {
            recommendations.push('Reduce existing debt to improve debt-to-income ratio');
        }
        if (creditScore < 600) {
            recommendations.push('Work on building credit history');
        }
        if (recommendations.length === 0) {
            recommendations.push('Application meets all criteria for approval');
        }
        return recommendations;
    }
    generateRejectionReason(riskAssessment, settings) {
        const reasons = [];
        if (riskAssessment.creditScore < settings.minCreditScore) {
            reasons.push(`Credit score below minimum requirement (${settings.minCreditScore})`);
        }
        if (riskAssessment.debtToIncomeRatio > settings.maxDebtToIncomeRatio) {
            reasons.push(`Debt-to-income ratio exceeds maximum allowed (${settings.maxDebtToIncomeRatio * 100}%)`);
        }
        if (riskAssessment.overallRiskScore < 50) {
            reasons.push('Overall risk assessment indicates high default probability');
        }
        return reasons.join('; ') || 'Application does not meet current lending criteria';
    }
}
exports.LoanService = LoanService;
exports.loanService = new LoanService();
//# sourceMappingURL=loanService.js.map