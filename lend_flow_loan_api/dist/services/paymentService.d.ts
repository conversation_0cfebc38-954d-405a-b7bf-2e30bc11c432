import { Payment, Loan, CreatePaymentRequest } from '../types';
export declare class PaymentService {
    processPayment(paymentData: CreatePaymentRequest, userId: string): Promise<Payment>;
    private calculatePaymentBreakdown;
    private updatePaymentSchedule;
    calculateEarlyPayoff(loan: Loan): {
        payoffAmount: number;
        interestSavings: number;
        payoffDate: string;
        remainingPayments: number;
        monthsSaved: number;
    };
    generatePaymentReminders(): Promise<void>;
    getPaymentStatistics(userId: string): {
        totalPayments: number;
        totalPaid: number;
        totalInterestPaid: number;
        totalLateFees: number;
        onTimePayments: number;
        latePayments: number;
        paymentHistoryScore: number;
        averagePaymentAmount: number;
    };
    validatePaymentAmount(loan: Loan, amount: number): {
        isValid: boolean;
        error?: string;
    };
}
export declare const paymentService: PaymentService;
//# sourceMappingURL=paymentService.d.ts.map