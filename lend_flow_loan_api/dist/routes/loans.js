"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const loanController_1 = require("../controllers/loanController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.use(auth_1.authenticateToken);
const validateLoanCalculation = [
    (0, express_validator_1.body)('amount')
        .isNumeric()
        .withMessage('Amount must be a number')
        .custom((value) => {
        if (value <= 0) {
            throw new Error('Amount must be greater than 0');
        }
        return true;
    }),
    (0, express_validator_1.body)('termMonths')
        .isInt({ min: 1, max: 120 })
        .withMessage('Term must be between 1 and 120 months'),
    (0, express_validator_1.body)('interestRate')
        .optional()
        .isFloat({ min: 0.1, max: 100 })
        .withMessage('Interest rate must be between 0.1% and 100%'),
    validation_1.handleValidationErrors
];
router.post('/applications', validation_1.validateLoanApplication, loanController_1.loanController.createLoanApplication);
router.get('/applications', validation_1.validatePaginationQuery, validation_1.handleValidationErrors, loanController_1.loanController.getLoanApplications);
router.get('/applications/:applicationId', (0, validation_1.validateUUIDParam)('applicationId'), validation_1.handleValidationErrors, loanController_1.loanController.getLoanApplication);
router.patch('/applications/:applicationId/cancel', (0, validation_1.validateUUIDParam)('applicationId'), validation_1.handleValidationErrors, loanController_1.loanController.cancelLoanApplication);
router.get('/', validation_1.validatePaginationQuery, validation_1.handleValidationErrors, loanController_1.loanController.getLoans);
router.get('/summary', loanController_1.loanController.getLoanSummary);
router.get('/:loanId', (0, validation_1.validateUUIDParam)('loanId'), validation_1.handleValidationErrors, loanController_1.loanController.getLoan);
router.patch('/:loanId', (0, validation_1.validateUUIDParam)('loanId'), validation_1.validateLoanUpdate, loanController_1.loanController.updateLoan);
router.post('/calculate', validateLoanCalculation, loanController_1.loanController.calculateLoan);
exports.default = router;
//# sourceMappingURL=loans.js.map