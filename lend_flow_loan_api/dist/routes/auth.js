"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = require("../controllers/authController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
const validateLogin = [
    ...validation_1.validatePhoneNumber,
    (0, express_validator_1.body)('pin')
        .isLength({ min: 4, max: 4 })
        .withMessage('PIN must be exactly 4 digits')
        .isNumeric()
        .withMessage('PIN must contain only numbers'),
    validation_1.handleValidationErrors
];
const validateOTP = [
    ...validation_1.validatePhoneNumber,
    (0, express_validator_1.body)('otp')
        .isLength({ min: 6, max: 6 })
        .withMessage('OTP must be exactly 6 digits')
        .isNumeric()
        .withMessage('OTP must contain only numbers'),
    validation_1.handleValidationErrors
];
const validateChangePIN = [
    (0, express_validator_1.body)('currentPin')
        .isLength({ min: 4, max: 4 })
        .withMessage('Current PIN must be exactly 4 digits')
        .isNumeric()
        .withMessage('Current PIN must contain only numbers'),
    (0, express_validator_1.body)('newPin')
        .isLength({ min: 4, max: 4 })
        .withMessage('New PIN must be exactly 4 digits')
        .isNumeric()
        .withMessage('New PIN must contain only numbers'),
    validation_1.handleValidationErrors
];
const validateResetPIN = [
    ...validation_1.validatePhoneNumber,
    (0, express_validator_1.body)('otp')
        .isLength({ min: 6, max: 6 })
        .withMessage('OTP must be exactly 6 digits')
        .isNumeric()
        .withMessage('OTP must contain only numbers'),
    (0, express_validator_1.body)('newPin')
        .isLength({ min: 4, max: 4 })
        .withMessage('New PIN must be exactly 4 digits')
        .isNumeric()
        .withMessage('New PIN must contain only numbers'),
    validation_1.handleValidationErrors
];
router.post('/login', validateLogin, authController_1.authController.login);
router.post('/send-otp', validation_1.validatePhoneNumber, validation_1.handleValidationErrors, authController_1.authController.sendOTP);
router.post('/verify-otp', validateOTP, authController_1.authController.verifyOTP);
router.post('/reset-pin', validateResetPIN, authController_1.authController.resetPIN);
router.use(auth_1.authenticateToken);
router.get('/me', authController_1.authController.getCurrentUser);
router.post('/change-pin', validateChangePIN, authController_1.authController.changePIN);
router.post('/refresh-token', authController_1.authController.refreshToken);
router.post('/logout', authController_1.authController.logout);
exports.default = router;
//# sourceMappingURL=auth.js.map