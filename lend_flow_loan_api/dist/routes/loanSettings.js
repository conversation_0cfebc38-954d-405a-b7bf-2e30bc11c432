"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const loanSettingsController_1 = require("../controllers/loanSettingsController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.use(auth_1.authenticateToken);
const validateEligibilityCheck = [
    (0, express_validator_1.body)('requestedAmount')
        .isNumeric()
        .withMessage('Requested amount must be a number')
        .custom((value) => {
        if (value <= 0) {
            throw new Error('Requested amount must be greater than 0');
        }
        return true;
    }),
    (0, express_validator_1.body)('termMonths')
        .isInt({ min: 1, max: 120 })
        .withMessage('Term must be between 1 and 120 months'),
    (0, express_validator_1.body)('monthlyIncome')
        .isNumeric()
        .withMessage('Monthly income must be a number')
        .custom((value) => {
        if (value <= 0) {
            throw new Error('Monthly income must be greater than 0');
        }
        return true;
    }),
    (0, express_validator_1.body)('existingDebts')
        .optional()
        .isNumeric()
        .withMessage('Existing debts must be a number')
        .custom((value) => {
        if (value < 0) {
            throw new Error('Existing debts cannot be negative');
        }
        return true;
    }),
    validation_1.handleValidationErrors
];
router.get('/', loanSettingsController_1.loanSettingsController.getLoanSettings);
router.get('/eligibility-criteria', loanSettingsController_1.loanSettingsController.getEligibilityCriteria);
router.post('/check-eligibility', validateEligibilityCheck, loanSettingsController_1.loanSettingsController.checkEligibility);
router.get('/interest-rate', loanSettingsController_1.loanSettingsController.getInterestRate);
exports.default = router;
//# sourceMappingURL=loanSettings.js.map