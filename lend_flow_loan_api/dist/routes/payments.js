"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const paymentController_1 = require("../controllers/paymentController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
router.use(auth_1.authenticateToken);
router.post('/', validation_1.validatePayment, paymentController_1.paymentController.createPayment);
router.get('/', validation_1.validatePaginationQuery, validation_1.handleValidationErrors, paymentController_1.paymentController.getUserPayments);
router.get('/summary', paymentController_1.paymentController.getPaymentSummary);
router.get('/:paymentId', (0, validation_1.validateUUIDParam)('paymentId'), validation_1.handleValidationErrors, paymentController_1.paymentController.getPayment);
router.get('/loans/:loanId', (0, validation_1.validateUUIDParam)('loanId'), validation_1.validatePaginationQuery, validation_1.handleValidationErrors, paymentController_1.paymentController.getLoanPayments);
router.get('/loans/:loanId/schedule', (0, validation_1.validateUUIDParam)('loanId'), validation_1.handleValidationErrors, paymentController_1.paymentController.getPaymentSchedule);
router.get('/loans/:loanId/early-payoff', (0, validation_1.validateUUIDParam)('loanId'), validation_1.handleValidationErrors, paymentController_1.paymentController.calculateEarlyPayoff);
exports.default = router;
//# sourceMappingURL=payments.js.map