"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const profileController_1 = require("../controllers/profileController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.use(auth_1.authenticateToken);
const validateNotificationPreferences = [
    (0, express_validator_1.body)('email')
        .optional()
        .isBoolean()
        .withMessage('Email preference must be a boolean'),
    (0, express_validator_1.body)('sms')
        .optional()
        .isBoolean()
        .withMessage('SMS preference must be a boolean'),
    (0, express_validator_1.body)('push')
        .optional()
        .isBoolean()
        .withMessage('Push preference must be a boolean'),
    (0, express_validator_1.body)('paymentReminders')
        .optional()
        .isBoolean()
        .withMessage('Payment reminders preference must be a boolean'),
    (0, express_validator_1.body)('loanUpdates')
        .optional()
        .isBoolean()
        .withMessage('Loan updates preference must be a boolean'),
    (0, express_validator_1.body)('promotional')
        .optional()
        .isBoolean()
        .withMessage('Promotional preference must be a boolean'),
    validation_1.handleValidationErrors
];
router.get('/', profileController_1.profileController.getProfile);
router.patch('/', validation_1.validateUserProfile, profileController_1.profileController.updateProfile);
router.delete('/', profileController_1.profileController.deleteAccount);
router.get('/financial-summary', profileController_1.profileController.getFinancialSummary);
router.get('/verification-status', profileController_1.profileController.getVerificationStatus);
router.get('/notifications', validation_1.validatePaginationQuery, validation_1.handleValidationErrors, profileController_1.profileController.getNotifications);
router.patch('/notifications/:notificationId/read', (0, validation_1.validateUUIDParam)('notificationId'), validation_1.handleValidationErrors, profileController_1.profileController.markNotificationAsRead);
router.patch('/notifications/read-all', profileController_1.profileController.markAllNotificationsAsRead);
router.get('/notification-preferences', profileController_1.profileController.getNotificationPreferences);
router.patch('/notification-preferences', validateNotificationPreferences, profileController_1.profileController.updateNotificationPreferences);
exports.default = router;
//# sourceMappingURL=profile.js.map