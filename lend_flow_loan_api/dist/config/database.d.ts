import { User, Loan, LoanApplication, Payment, PaymentSchedule, LoanSettings, Notification } from '../types';
export declare class InMemoryDatabase {
    private users;
    private loans;
    private loanApplications;
    private payments;
    private paymentSchedules;
    private loanSettings;
    private notifications;
    private riskAssessments;
    private creditChecks;
    constructor();
    createUser(user: User): User;
    getUserById(id: string): User | undefined;
    getUserByPhone(phoneNumber: string): User | undefined;
    updateUser(id: string, updates: Partial<User>): User | undefined;
    getAllUsers(): User[];
    createLoan(loan: Loan): Loan;
    getLoanById(id: string): Loan | undefined;
    getLoansByUserId(userId: string): Loan[];
    updateLoan(id: string, updates: Partial<Loan>): Loan | undefined;
    getAllLoans(): Loan[];
    createLoanApplication(application: LoanApplication): LoanApplication;
    getLoanApplicationById(id: string): LoanApplication | undefined;
    getLoanApplicationsByUserId(userId: string): LoanApplication[];
    updateLoanApplication(id: string, updates: Partial<LoanApplication>): LoanApplication | undefined;
    createPayment(payment: Payment): Payment;
    getPaymentById(id: string): Payment | undefined;
    getPaymentsByLoanId(loanId: string): Payment[];
    getPaymentsByUserId(userId: string): Payment[];
    createPaymentSchedule(schedule: PaymentSchedule): PaymentSchedule;
    getPaymentSchedulesByLoanId(loanId: string): PaymentSchedule[];
    updatePaymentSchedule(id: string, updates: Partial<PaymentSchedule>): PaymentSchedule | undefined;
    getLoanSettings(): LoanSettings | undefined;
    updateLoanSettings(id: string, updates: Partial<LoanSettings>): LoanSettings | undefined;
    createNotification(notification: Notification): Notification;
    getNotificationsByUserId(userId: string): Notification[];
    updateNotification(id: string, updates: Partial<Notification>): Notification | undefined;
    private initializeDefaultData;
}
export declare const database: InMemoryDatabase;
//# sourceMappingURL=database.d.ts.map