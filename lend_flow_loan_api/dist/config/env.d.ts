export declare const config: {
    PORT: string | number;
    NODE_ENV: string;
    JWT_SECRET: string;
    JWT_EXPIRES_IN: string;
    DATABASE_URL: string;
    API_VERSION: string;
    API_PREFIX: string;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    CORS_ORIGIN: string;
    MAX_FILE_SIZE: number;
    UPLOAD_PATH: string;
    ENABLE_SMS: boolean;
    ENABLE_EMAIL: boolean;
    SMS_API_KEY: string;
    SMS_API_URL: string;
    EMAIL_HOST: string;
    EMAIL_PORT: number;
    EMAIL_USER: string;
    EMAIL_PASS: string;
    PAYMENT_GATEWAY_URL: string;
    PAYMENT_GATEWAY_KEY: string;
    PAYMENT_GATEWAY_SECRET: string;
    CREDIT_CHECK_API_URL: string;
    CREDIT_CHECK_API_KEY: string;
    LOG_LEVEL: string;
    LOG_FILE: string;
    BCRYPT_ROUNDS: number;
    DEFAULT_INTEREST_RATE: number;
    MAX_LOAN_AMOUNT: number;
    MIN_LOAN_AMOUNT: number;
    MAX_LOAN_TERM_MONTHS: number;
    MIN_LOAN_TERM_MONTHS: number;
    ENABLE_CREDIT_CHECK: boolean;
    ENABLE_RISK_ASSESSMENT: boolean;
    ENABLE_AUTO_APPROVAL: boolean;
    ENABLE_SWAGGER: boolean;
    ENABLE_MORGAN_LOGGING: boolean;
};
export declare const validateConfig: () => void;
export declare const isDevelopment: () => boolean;
export declare const isProduction: () => boolean;
export declare const isTest: () => boolean;
//# sourceMappingURL=env.d.ts.map