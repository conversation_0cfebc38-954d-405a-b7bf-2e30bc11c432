"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isTest = exports.isProduction = exports.isDevelopment = exports.validateConfig = exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
exports.config = {
    PORT: process.env.PORT || 3000,
    NODE_ENV: process.env.NODE_ENV || 'development',
    JWT_SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
    DATABASE_URL: process.env.DATABASE_URL || '',
    API_VERSION: process.env.API_VERSION || 'v1',
    API_PREFIX: process.env.API_PREFIX || '/api',
    RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
    RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    CORS_ORIGIN: process.env.CORS_ORIGIN || '*',
    MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || '5242880'),
    UPLOAD_PATH: process.env.UPLOAD_PATH || './uploads',
    ENABLE_SMS: process.env.ENABLE_SMS === 'true',
    ENABLE_EMAIL: process.env.ENABLE_EMAIL === 'true',
    SMS_API_KEY: process.env.SMS_API_KEY || '',
    SMS_API_URL: process.env.SMS_API_URL || '',
    EMAIL_HOST: process.env.EMAIL_HOST || '',
    EMAIL_PORT: parseInt(process.env.EMAIL_PORT || '587'),
    EMAIL_USER: process.env.EMAIL_USER || '',
    EMAIL_PASS: process.env.EMAIL_PASS || '',
    PAYMENT_GATEWAY_URL: process.env.PAYMENT_GATEWAY_URL || '',
    PAYMENT_GATEWAY_KEY: process.env.PAYMENT_GATEWAY_KEY || '',
    PAYMENT_GATEWAY_SECRET: process.env.PAYMENT_GATEWAY_SECRET || '',
    CREDIT_CHECK_API_URL: process.env.CREDIT_CHECK_API_URL || '',
    CREDIT_CHECK_API_KEY: process.env.CREDIT_CHECK_API_KEY || '',
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    LOG_FILE: process.env.LOG_FILE || './logs/app.log',
    BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || '12'),
    DEFAULT_INTEREST_RATE: parseFloat(process.env.DEFAULT_INTEREST_RATE || '12'),
    MAX_LOAN_AMOUNT: parseFloat(process.env.MAX_LOAN_AMOUNT || '100000'),
    MIN_LOAN_AMOUNT: parseFloat(process.env.MIN_LOAN_AMOUNT || '1000'),
    MAX_LOAN_TERM_MONTHS: parseInt(process.env.MAX_LOAN_TERM_MONTHS || '60'),
    MIN_LOAN_TERM_MONTHS: parseInt(process.env.MIN_LOAN_TERM_MONTHS || '6'),
    ENABLE_CREDIT_CHECK: process.env.ENABLE_CREDIT_CHECK === 'true',
    ENABLE_RISK_ASSESSMENT: process.env.ENABLE_RISK_ASSESSMENT === 'true',
    ENABLE_AUTO_APPROVAL: process.env.ENABLE_AUTO_APPROVAL === 'true',
    ENABLE_SWAGGER: process.env.ENABLE_SWAGGER !== 'false',
    ENABLE_MORGAN_LOGGING: process.env.ENABLE_MORGAN_LOGGING !== 'false',
};
const validateConfig = () => {
    const requiredVars = ['JWT_SECRET'];
    for (const varName of requiredVars) {
        if (!process.env[varName] && exports.config.NODE_ENV === 'production') {
            throw new Error(`Required environment variable ${varName} is not set`);
        }
    }
    if (isNaN(exports.config.PORT)) {
        throw new Error('PORT must be a valid number');
    }
    if (isNaN(exports.config.RATE_LIMIT_WINDOW_MS)) {
        throw new Error('RATE_LIMIT_WINDOW_MS must be a valid number');
    }
    if (isNaN(exports.config.RATE_LIMIT_MAX_REQUESTS)) {
        throw new Error('RATE_LIMIT_MAX_REQUESTS must be a valid number');
    }
};
exports.validateConfig = validateConfig;
const isDevelopment = () => exports.config.NODE_ENV === 'development';
exports.isDevelopment = isDevelopment;
const isProduction = () => exports.config.NODE_ENV === 'production';
exports.isProduction = isProduction;
const isTest = () => exports.config.NODE_ENV === 'test';
exports.isTest = isTest;
//# sourceMappingURL=env.js.map