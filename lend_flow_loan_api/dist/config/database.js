"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.database = exports.InMemoryDatabase = void 0;
class InMemoryDatabase {
    constructor() {
        this.users = new Map();
        this.loans = new Map();
        this.loanApplications = new Map();
        this.payments = new Map();
        this.paymentSchedules = new Map();
        this.loanSettings = new Map();
        this.notifications = new Map();
        this.riskAssessments = new Map();
        this.creditChecks = new Map();
        this.initializeDefaultData();
    }
    createUser(user) {
        this.users.set(user.id, user);
        return user;
    }
    getUserById(id) {
        return this.users.get(id);
    }
    getUserByPhone(phoneNumber) {
        return Array.from(this.users.values()).find(user => user.phoneNumber === phoneNumber);
    }
    updateUser(id, updates) {
        const user = this.users.get(id);
        if (user) {
            const updatedUser = { ...user, ...updates, updatedAt: new Date() };
            this.users.set(id, updatedUser);
            return updatedUser;
        }
        return undefined;
    }
    getAllUsers() {
        return Array.from(this.users.values());
    }
    createLoan(loan) {
        this.loans.set(loan.id, loan);
        return loan;
    }
    getLoanById(id) {
        return this.loans.get(id);
    }
    getLoansByUserId(userId) {
        return Array.from(this.loans.values()).filter(loan => loan.userId === userId);
    }
    updateLoan(id, updates) {
        const loan = this.loans.get(id);
        if (loan) {
            const updatedLoan = { ...loan, ...updates, updatedAt: new Date() };
            this.loans.set(id, updatedLoan);
            return updatedLoan;
        }
        return undefined;
    }
    getAllLoans() {
        return Array.from(this.loans.values());
    }
    createLoanApplication(application) {
        this.loanApplications.set(application.id, application);
        return application;
    }
    getLoanApplicationById(id) {
        return this.loanApplications.get(id);
    }
    getLoanApplicationsByUserId(userId) {
        return Array.from(this.loanApplications.values()).filter(app => app.userId === userId);
    }
    updateLoanApplication(id, updates) {
        const application = this.loanApplications.get(id);
        if (application) {
            const updatedApplication = { ...application, ...updates, updatedAt: new Date() };
            this.loanApplications.set(id, updatedApplication);
            return updatedApplication;
        }
        return undefined;
    }
    createPayment(payment) {
        this.payments.set(payment.id, payment);
        return payment;
    }
    getPaymentById(id) {
        return this.payments.get(id);
    }
    getPaymentsByLoanId(loanId) {
        return Array.from(this.payments.values()).filter(payment => payment.loanId === loanId);
    }
    getPaymentsByUserId(userId) {
        const userLoans = this.getLoansByUserId(userId);
        const loanIds = userLoans.map(loan => loan.id);
        return Array.from(this.payments.values()).filter(payment => loanIds.includes(payment.loanId));
    }
    createPaymentSchedule(schedule) {
        this.paymentSchedules.set(schedule.id, schedule);
        return schedule;
    }
    getPaymentSchedulesByLoanId(loanId) {
        return Array.from(this.paymentSchedules.values())
            .filter(schedule => schedule.loanId === loanId)
            .sort((a, b) => a.paymentNumber - b.paymentNumber);
    }
    updatePaymentSchedule(id, updates) {
        const schedule = this.paymentSchedules.get(id);
        if (schedule) {
            const updatedSchedule = { ...schedule, ...updates, updatedAt: new Date() };
            this.paymentSchedules.set(id, updatedSchedule);
            return updatedSchedule;
        }
        return undefined;
    }
    getLoanSettings() {
        const activeSettings = Array.from(this.loanSettings.values()).find(settings => settings.isActive);
        return activeSettings;
    }
    updateLoanSettings(id, updates) {
        const settings = this.loanSettings.get(id);
        if (settings) {
            const updatedSettings = { ...settings, ...updates, updatedAt: new Date() };
            this.loanSettings.set(id, updatedSettings);
            return updatedSettings;
        }
        return undefined;
    }
    createNotification(notification) {
        this.notifications.set(notification.id, notification);
        return notification;
    }
    getNotificationsByUserId(userId) {
        return Array.from(this.notifications.values())
            .filter(notification => notification.userId === userId)
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
    updateNotification(id, updates) {
        const notification = this.notifications.get(id);
        if (notification) {
            const updatedNotification = { ...notification, ...updates };
            this.notifications.set(id, updatedNotification);
            return updatedNotification;
        }
        return undefined;
    }
    initializeDefaultData() {
        const defaultSettings = {
            id: 'default-settings',
            minLoanAmount: 1000,
            maxLoanAmount: 100000,
            minTermMonths: 6,
            maxTermMonths: 60,
            baseInterestRate: 12,
            maxInterestRate: 30,
            lateFeePercentage: 5,
            lateFeeFixedAmount: 50,
            gracePeriodDays: 7,
            maxDebtToIncomeRatio: 0.4,
            minCreditScore: 300,
            processingFeePercentage: 2,
            processingFeeFixedAmount: 100,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.loanSettings.set(defaultSettings.id, defaultSettings);
        const demoUser = {
            id: 'user-**********',
            phoneNumber: '**********',
            firstName: 'Demo',
            lastName: 'User',
            email: '<EMAIL>',
            address: '123 Demo Street, Demo City',
            creditScore: 720,
            isActive: true,
            isVerified: true,
            bankingInfo: {
                accountNumber: '**********',
                bankName: 'Demo Bank',
                accountType: 'savings',
                accountHolderName: 'Demo User'
            },
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date()
        };
        this.users.set(demoUser.id, demoUser);
    }
}
exports.InMemoryDatabase = InMemoryDatabase;
exports.database = new InMemoryDatabase();
//# sourceMappingURL=database.js.map