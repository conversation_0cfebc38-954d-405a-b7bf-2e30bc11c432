"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const env_1 = require("./config/env");
const database_1 = require("./config/database");
const app = (0, express_1.default)();
const swaggerOptions = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'Lend Flow Loan API',
            version: '1.0.0',
            description: 'A comprehensive RESTful API for loan management, built with Node.js, Express, and TypeScript. This API provides complete loan lifecycle management including applications, approvals, payments, and user profile management.',
            contact: {
                name: 'Lend Flow API Support',
                email: '<EMAIL>'
            },
            servers: [
                {
                    url: `http://localhost:${env_1.config.PORT}${env_1.config.API_PREFIX}/${env_1.config.API_VERSION}`,
                    description: 'Development server'
                }
            ]
        },
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT'
                }
            }
        },
        security: [
            {
                bearerAuth: []
            }
        ]
    },
    apis: ['./src/app.ts']
};
const swaggerSpec = (0, swagger_jsdoc_1.default)(swaggerOptions);
app.use((0, helmet_1.default)({
    contentSecurityPolicy: (0, env_1.isDevelopment)() ? false : undefined,
}));
app.use((0, cors_1.default)({
    origin: env_1.config.CORS_ORIGIN,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use((0, compression_1.default)());
if (env_1.config.ENABLE_MORGAN_LOGGING) {
    app.use((0, morgan_1.default)((0, env_1.isDevelopment)() ? 'dev' : 'combined'));
}
if (env_1.config.ENABLE_SWAGGER) {
    app.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swaggerSpec));
}
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Lend Flow API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: env_1.config.NODE_ENV,
    });
});
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({
            success: false,
            error: 'Access token required'
        });
    }
    try {
        if (token.startsWith('demo-')) {
            req.user = { id: 'user-**********', phoneNumber: '**********' };
            next();
        }
        else {
            res.status(401).json({
                success: false,
                error: 'Invalid token'
            });
        }
    }
    catch (error) {
        res.status(401).json({
            success: false,
            error: 'Invalid token'
        });
    }
};
const apiPrefix = `${env_1.config.API_PREFIX}/${env_1.config.API_VERSION}`;
app.post(`${apiPrefix}/auth/login`, (req, res) => {
    const { phoneNumber, pin } = req.body;
    if (phoneNumber === '**********' && pin === '9999') {
        const token = 'demo-token-' + Date.now();
        res.json({
            success: true,
            data: {
                user: {
                    id: 'user-**********',
                    phoneNumber: '**********',
                    firstName: 'Demo',
                    lastName: 'User',
                    email: '<EMAIL>'
                },
                token
            },
            message: 'Login successful'
        });
    }
    else {
        res.status(401).json({
            success: false,
            error: 'Invalid credentials'
        });
    }
});
app.get(`${apiPrefix}/loans`, authenticateToken, (req, res) => {
    const loans = database_1.database.getLoansByUserId(req.user.id);
    res.json({
        success: true,
        data: loans
    });
});
app.get(`${apiPrefix}/loans/summary`, authenticateToken, (req, res) => {
    const loans = database_1.database.getLoansByUserId(req.user.id);
    const activeLoans = loans.filter((loan) => loan.status === 'active');
    const summary = {
        totalLoans: loans.length,
        activeLoans: activeLoans.length,
        totalOutstanding: activeLoans.reduce((sum, loan) => sum + loan.remainingBalance, 0),
        totalBorrowed: loans.reduce((sum, loan) => sum + loan.amount, 0)
    };
    res.json({
        success: true,
        data: summary
    });
});
app.post(`${apiPrefix}/loans/calculate`, (req, res) => {
    const { amount, termMonths, interestRate = 12 } = req.body;
    if (!amount || !termMonths) {
        res.status(400).json({
            success: false,
            error: 'Amount and term are required'
        });
        return;
    }
    const monthlyRate = interestRate / 100 / 12;
    let monthlyPayment;
    if (monthlyRate === 0) {
        monthlyPayment = amount / termMonths;
    }
    else {
        const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
        const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
        monthlyPayment = amount * (numerator / denominator);
    }
    const totalAmount = monthlyPayment * termMonths;
    const totalInterest = totalAmount - amount;
    res.json({
        success: true,
        data: {
            principal: amount,
            interestRate,
            termMonths,
            monthlyPayment: Math.round(monthlyPayment * 100) / 100,
            totalInterest: Math.round(totalInterest * 100) / 100,
            totalAmount: Math.round(totalAmount * 100) / 100
        }
    });
});
app.get(`${apiPrefix}/payments`, authenticateToken, (req, res) => {
    const payments = database_1.database.getPaymentsByUserId(req.user.id);
    res.json({
        success: true,
        data: payments
    });
});
app.get(`${apiPrefix}/profile`, authenticateToken, (req, res) => {
    const user = database_1.database.getUserById(req.user.id);
    res.json({
        success: true,
        data: user
    });
});
app.get(`${apiPrefix}/loan-settings`, (req, res) => {
    const settings = database_1.database.getLoanSettings();
    res.json({
        success: true,
        data: settings
    });
});
app.get(`${apiPrefix}/docs`, (req, res) => {
    res.json({
        success: true,
        message: 'Lend Flow API Documentation',
        version: '1.0.0',
        endpoints: {
            'POST /auth/login': 'Login with phone number and PIN',
            'GET /loans': 'Get user loans',
            'GET /loans/summary': 'Get loan summary',
            'POST /loans/calculate': 'Calculate loan details',
            'GET /payments': 'Get user payments',
            'GET /profile': 'Get user profile',
            'GET /loan-settings': 'Get loan settings'
        },
        demoCredentials: {
            phoneNumber: '**********',
            pin: '9999'
        }
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        message: `The requested endpoint ${req.method} ${req.originalUrl} was not found.`,
    });
});
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);
    res.status(error.status || 500).json({
        success: false,
        error: (0, env_1.isDevelopment)() ? error.message : 'Internal server error',
        ...((0, env_1.isDevelopment)() && { stack: error.stack }),
    });
});
exports.default = app;
//# sourceMappingURL=app.js.map