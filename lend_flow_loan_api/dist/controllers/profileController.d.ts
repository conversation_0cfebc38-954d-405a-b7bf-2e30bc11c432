import { Request, Response } from 'express';
export declare class ProfileController {
    getProfile(req: Request, res: Response): Promise<void>;
    updateProfile(req: Request, res: Response): Promise<void>;
    getFinancialSummary(req: Request, res: Response): Promise<void>;
    getNotifications(req: Request, res: Response): Promise<void>;
    markNotificationAsRead(req: Request, res: Response): Promise<void>;
    markAllNotificationsAsRead(req: Request, res: Response): Promise<void>;
    getNotificationPreferences(req: Request, res: Response): Promise<void>;
    updateNotificationPreferences(req: Request, res: Response): Promise<void>;
    deleteAccount(req: Request, res: Response): Promise<void>;
    getVerificationStatus(req: Request, res: Response): Promise<void>;
    private getNextPaymentInfo;
    private calculateProfileCompletion;
}
export declare const profileController: ProfileController;
//# sourceMappingURL=profileController.d.ts.map