import { Request, Response } from 'express';
export declare class LoanController {
    createLoanApplication(req: Request, res: Response): Promise<void>;
    getLoanApplications(req: Request, res: Response): Promise<void>;
    getLoanApplication(req: Request, res: Response): Promise<void>;
    getLoans(req: Request, res: Response): Promise<void>;
    getLoan(req: Request, res: Response): Promise<void>;
    updateLoan(req: Request, res: Response): Promise<void>;
    cancelLoanApplication(req: Request, res: Response): Promise<void>;
    getLoanSummary(req: Request, res: Response): Promise<void>;
    calculateLoan(req: Request, res: Response): Promise<void>;
}
export declare const loanController: LoanController;
//# sourceMappingURL=loanController.d.ts.map