"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentController = exports.PaymentController = void 0;
const database_1 = require("../config/database");
const paymentService_1 = require("../services/paymentService");
class PaymentController {
    async createPayment(req, res) {
        try {
            const userId = req.user.id;
            const paymentData = req.body;
            const loan = database_1.database.getLoanById(paymentData.loanId);
            if (!loan) {
                res.status(404).json({
                    success: false,
                    error: 'Loan not found'
                });
                return;
            }
            if (loan.userId !== userId) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied'
                });
                return;
            }
            if (loan.status !== 'active') {
                res.status(400).json({
                    success: false,
                    error: 'Cannot make payment on inactive loan'
                });
                return;
            }
            const payment = await paymentService_1.paymentService.processPayment(paymentData, userId);
            res.status(201).json({
                success: true,
                data: payment,
                message: 'Payment processed successfully'
            });
        }
        catch (error) {
            console.error('Error creating payment:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Failed to process payment'
            });
        }
    }
    async getLoanPayments(req, res) {
        try {
            const { loanId } = req.params;
            const userId = req.user.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;
            const loan = database_1.database.getLoanById(loanId);
            if (!loan) {
                res.status(404).json({
                    success: false,
                    error: 'Loan not found'
                });
                return;
            }
            if (loan.userId !== userId) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied'
                });
                return;
            }
            const allPayments = database_1.database.getPaymentsByLoanId(loanId);
            const total = allPayments.length;
            const payments = allPayments
                .sort((a, b) => b.paymentDate.getTime() - a.paymentDate.getTime())
                .slice(offset, offset + limit);
            const pagination = {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: page < Math.ceil(total / limit),
                hasPrev: page > 1
            };
            res.json({
                success: true,
                data: payments,
                pagination
            });
        }
        catch (error) {
            console.error('Error fetching loan payments:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch payments'
            });
        }
    }
    async getUserPayments(req, res) {
        try {
            const userId = req.user.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const status = req.query.status;
            const offset = (page - 1) * limit;
            let payments = database_1.database.getPaymentsByUserId(userId);
            if (status) {
                payments = payments.filter(payment => payment.status === status);
            }
            const total = payments.length;
            const paginatedPayments = payments
                .sort((a, b) => b.paymentDate.getTime() - a.paymentDate.getTime())
                .slice(offset, offset + limit);
            const pagination = {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: page < Math.ceil(total / limit),
                hasPrev: page > 1
            };
            res.json({
                success: true,
                data: paginatedPayments,
                pagination
            });
        }
        catch (error) {
            console.error('Error fetching user payments:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch payments'
            });
        }
    }
    async getPaymentSchedule(req, res) {
        try {
            const { loanId } = req.params;
            const userId = req.user.id;
            const loan = database_1.database.getLoanById(loanId);
            if (!loan) {
                res.status(404).json({
                    success: false,
                    error: 'Loan not found'
                });
                return;
            }
            if (loan.userId !== userId) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied'
                });
                return;
            }
            const schedule = database_1.database.getPaymentSchedulesByLoanId(loanId);
            res.json({
                success: true,
                data: schedule
            });
        }
        catch (error) {
            console.error('Error fetching payment schedule:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch payment schedule'
            });
        }
    }
    async getPayment(req, res) {
        try {
            const { paymentId } = req.params;
            const userId = req.user.id;
            const payment = database_1.database.getPaymentById(paymentId);
            if (!payment) {
                res.status(404).json({
                    success: false,
                    error: 'Payment not found'
                });
                return;
            }
            const loan = database_1.database.getLoanById(payment.loanId);
            if (!loan || loan.userId !== userId) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied'
                });
                return;
            }
            res.json({
                success: true,
                data: payment
            });
        }
        catch (error) {
            console.error('Error fetching payment:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch payment'
            });
        }
    }
    async getPaymentSummary(req, res) {
        try {
            const userId = req.user.id;
            const payments = database_1.database.getPaymentsByUserId(userId);
            const loans = database_1.database.getLoansByUserId(userId);
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth();
            const currentYear = currentDate.getFullYear();
            const summary = {
                totalPayments: payments.length,
                totalAmountPaid: payments
                    .filter(p => p.status === 'completed')
                    .reduce((sum, payment) => sum + payment.amount, 0),
                paymentsThisMonth: payments.filter(payment => {
                    const paymentDate = new Date(payment.paymentDate);
                    return paymentDate.getMonth() === currentMonth &&
                        paymentDate.getFullYear() === currentYear &&
                        payment.status === 'completed';
                }).length,
                pendingPayments: payments.filter(p => p.status === 'pending').length,
                overduePayments: payments.filter(p => {
                    return p.status === 'pending' && new Date(p.dueDate) < currentDate;
                }).length,
                nextPaymentDue: this.getNextPaymentDue(loans),
                recentPayments: payments
                    .filter(p => p.status === 'completed')
                    .sort((a, b) => b.paymentDate.getTime() - a.paymentDate.getTime())
                    .slice(0, 5)
            };
            res.json({
                success: true,
                data: summary
            });
        }
        catch (error) {
            console.error('Error fetching payment summary:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch payment summary'
            });
        }
    }
    async calculateEarlyPayoff(req, res) {
        try {
            const { loanId } = req.params;
            const userId = req.user.id;
            const loan = database_1.database.getLoanById(loanId);
            if (!loan) {
                res.status(404).json({
                    success: false,
                    error: 'Loan not found'
                });
                return;
            }
            if (loan.userId !== userId) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied'
                });
                return;
            }
            if (loan.status !== 'active') {
                res.status(400).json({
                    success: false,
                    error: 'Loan is not active'
                });
                return;
            }
            const payoffCalculation = paymentService_1.paymentService.calculateEarlyPayoff(loan);
            res.json({
                success: true,
                data: payoffCalculation
            });
        }
        catch (error) {
            console.error('Error calculating early payoff:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to calculate early payoff'
            });
        }
    }
    getNextPaymentDue(loans) {
        const activeLoans = loans.filter(loan => loan.status === 'active');
        if (activeLoans.length === 0)
            return null;
        const nextPayments = activeLoans
            .filter(loan => loan.nextPaymentDate)
            .map(loan => ({
            loanId: loan.id,
            amount: loan.monthlyPayment,
            dueDate: loan.nextPaymentDate,
            loanPurpose: loan.purpose
        }))
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
        return nextPayments[0] || null;
    }
}
exports.PaymentController = PaymentController;
exports.paymentController = new PaymentController();
//# sourceMappingURL=paymentController.js.map