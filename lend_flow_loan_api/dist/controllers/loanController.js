"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loanController = exports.LoanController = void 0;
const uuid_1 = require("uuid");
const database_1 = require("../config/database");
const loanService_1 = require("../services/loanService");
class LoanController {
    async createLoanApplication(req, res) {
        try {
            const userId = req.user.id;
            const applicationData = req.body;
            const existingApplications = database_1.database.getLoanApplicationsByUserId(userId);
            const pendingApplication = existingApplications.find(app => app.status === 'submitted' || app.status === 'under_review');
            if (pendingApplication) {
                res.status(400).json({
                    success: false,
                    error: 'You already have a pending loan application'
                });
                return;
            }
            const application = {
                id: (0, uuid_1.v4)(),
                userId,
                requestedAmount: applicationData.requestedAmount,
                purpose: applicationData.purpose,
                termMonths: applicationData.termMonths,
                employmentInfo: applicationData.employmentInfo,
                monthlyIncome: applicationData.monthlyIncome,
                existingDebts: applicationData.existingDebts,
                status: 'submitted',
                documents: [],
                createdAt: new Date(),
                updatedAt: new Date()
            };
            const createdApplication = database_1.database.createLoanApplication(application);
            const riskAssessment = await loanService_1.loanService.performRiskAssessment(createdApplication);
            const updatedApplication = await loanService_1.loanService.processApplication(createdApplication.id);
            res.status(201).json({
                success: true,
                data: updatedApplication,
                message: 'Loan application submitted successfully'
            });
        }
        catch (error) {
            console.error('Error creating loan application:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to create loan application'
            });
        }
    }
    async getLoanApplications(req, res) {
        try {
            const userId = req.user.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;
            const allApplications = database_1.database.getLoanApplicationsByUserId(userId);
            const total = allApplications.length;
            const applications = allApplications.slice(offset, offset + limit);
            const pagination = {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: page < Math.ceil(total / limit),
                hasPrev: page > 1
            };
            res.json({
                success: true,
                data: applications,
                pagination
            });
        }
        catch (error) {
            console.error('Error fetching loan applications:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch loan applications'
            });
        }
    }
    async getLoanApplication(req, res) {
        try {
            const { applicationId } = req.params;
            const userId = req.user.id;
            const application = database_1.database.getLoanApplicationById(applicationId);
            if (!application) {
                res.status(404).json({
                    success: false,
                    error: 'Loan application not found'
                });
                return;
            }
            if (application.userId !== userId) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied'
                });
                return;
            }
            res.json({
                success: true,
                data: application
            });
        }
        catch (error) {
            console.error('Error fetching loan application:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch loan application'
            });
        }
    }
    async getLoans(req, res) {
        try {
            const userId = req.user.id;
            const status = req.query.status;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;
            let loans = database_1.database.getLoansByUserId(userId);
            if (status) {
                loans = loans.filter(loan => loan.status === status);
            }
            const total = loans.length;
            const paginatedLoans = loans.slice(offset, offset + limit);
            const pagination = {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: page < Math.ceil(total / limit),
                hasPrev: page > 1
            };
            res.json({
                success: true,
                data: paginatedLoans,
                pagination
            });
        }
        catch (error) {
            console.error('Error fetching loans:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch loans'
            });
        }
    }
    async getLoan(req, res) {
        try {
            const { loanId } = req.params;
            const userId = req.user.id;
            const loan = database_1.database.getLoanById(loanId);
            if (!loan) {
                res.status(404).json({
                    success: false,
                    error: 'Loan not found'
                });
                return;
            }
            if (loan.userId !== userId) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied'
                });
                return;
            }
            res.json({
                success: true,
                data: loan
            });
        }
        catch (error) {
            console.error('Error fetching loan:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch loan'
            });
        }
    }
    async updateLoan(req, res) {
        try {
            const { loanId } = req.params;
            const updates = req.body;
            const loan = database_1.database.getLoanById(loanId);
            if (!loan) {
                res.status(404).json({
                    success: false,
                    error: 'Loan not found'
                });
                return;
            }
            const updatedLoan = database_1.database.updateLoan(loanId, updates);
            res.json({
                success: true,
                data: updatedLoan,
                message: 'Loan updated successfully'
            });
        }
        catch (error) {
            console.error('Error updating loan:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to update loan'
            });
        }
    }
    async cancelLoanApplication(req, res) {
        try {
            const { applicationId } = req.params;
            const userId = req.user.id;
            const application = database_1.database.getLoanApplicationById(applicationId);
            if (!application) {
                res.status(404).json({
                    success: false,
                    error: 'Loan application not found'
                });
                return;
            }
            if (application.userId !== userId) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied'
                });
                return;
            }
            if (application.status !== 'submitted' && application.status !== 'under_review') {
                res.status(400).json({
                    success: false,
                    error: 'Cannot cancel application in current status'
                });
                return;
            }
            const updatedApplication = database_1.database.updateLoanApplication(applicationId, {
                status: 'cancelled'
            });
            res.json({
                success: true,
                data: updatedApplication,
                message: 'Loan application cancelled successfully'
            });
        }
        catch (error) {
            console.error('Error cancelling loan application:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to cancel loan application'
            });
        }
    }
    async getLoanSummary(req, res) {
        try {
            const userId = req.user.id;
            const loans = database_1.database.getLoansByUserId(userId);
            const summary = {
                totalLoans: loans.length,
                activeLoans: loans.filter(loan => loan.status === 'active').length,
                completedLoans: loans.filter(loan => loan.status === 'completed').length,
                totalBorrowed: loans.reduce((sum, loan) => sum + loan.amount, 0),
                totalOutstanding: loans
                    .filter(loan => loan.status === 'active')
                    .reduce((sum, loan) => sum + loan.remainingBalance, 0),
                totalPaid: loans.reduce((sum, loan) => sum + (loan.amount - loan.remainingBalance), 0),
                nextPaymentDue: loans
                    .filter(loan => loan.status === 'active' && loan.nextPaymentDate)
                    .sort((a, b) => new Date(a.nextPaymentDate).getTime() - new Date(b.nextPaymentDate).getTime())[0]
            };
            res.json({
                success: true,
                data: summary
            });
        }
        catch (error) {
            console.error('Error fetching loan summary:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch loan summary'
            });
        }
    }
    async calculateLoan(req, res) {
        try {
            const { amount, termMonths, interestRate } = req.body;
            if (!amount || !termMonths) {
                res.status(400).json({
                    success: false,
                    error: 'Amount and term are required'
                });
                return;
            }
            const calculation = loanService_1.loanService.calculateLoanDetails(amount, interestRate || 12, termMonths);
            res.json({
                success: true,
                data: calculation
            });
        }
        catch (error) {
            console.error('Error calculating loan:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to calculate loan'
            });
        }
    }
}
exports.LoanController = LoanController;
exports.loanController = new LoanController();
//# sourceMappingURL=loanController.js.map