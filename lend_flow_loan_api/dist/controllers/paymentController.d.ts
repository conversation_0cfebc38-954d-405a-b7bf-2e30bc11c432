import { Request, Response } from 'express';
export declare class PaymentController {
    createPayment(req: Request, res: Response): Promise<void>;
    getLoanPayments(req: Request, res: Response): Promise<void>;
    getUserPayments(req: Request, res: Response): Promise<void>;
    getPaymentSchedule(req: Request, res: Response): Promise<void>;
    getPayment(req: Request, res: Response): Promise<void>;
    getPaymentSummary(req: Request, res: Response): Promise<void>;
    calculateEarlyPayoff(req: Request, res: Response): Promise<void>;
    private getNextPaymentDue;
}
export declare const paymentController: PaymentController;
//# sourceMappingURL=paymentController.d.ts.map