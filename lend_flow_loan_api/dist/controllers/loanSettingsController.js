"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loanSettingsController = exports.LoanSettingsController = void 0;
const uuid_1 = require("uuid");
const database_1 = require("../config/database");
class LoanSettingsController {
    async getLoanSettings(req, res) {
        try {
            const settings = database_1.database.getLoanSettings();
            if (!settings) {
                res.status(404).json({
                    success: false,
                    error: 'Loan settings not found'
                });
                return;
            }
            res.json({
                success: true,
                data: settings
            });
        }
        catch (error) {
            console.error('Error fetching loan settings:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch loan settings'
            });
        }
    }
    async updateLoanSettings(req, res) {
        try {
            const updates = req.body;
            const currentSettings = database_1.database.getLoanSettings();
            if (!currentSettings) {
                const newSettings = {
                    id: (0, uuid_1.v4)(),
                    minLoanAmount: updates.minLoanAmount || 1000,
                    maxLoanAmount: updates.maxLoanAmount || 100000,
                    minTermMonths: updates.minTermMonths || 6,
                    maxTermMonths: updates.maxTermMonths || 60,
                    baseInterestRate: updates.baseInterestRate || 12,
                    maxInterestRate: updates.maxInterestRate || 30,
                    lateFeePercentage: updates.lateFeePercentage || 5,
                    lateFeeFixedAmount: updates.lateFeeFixedAmount || 50,
                    gracePeriodDays: updates.gracePeriodDays || 7,
                    maxDebtToIncomeRatio: updates.maxDebtToIncomeRatio || 0.4,
                    minCreditScore: updates.minCreditScore || 300,
                    processingFeePercentage: updates.processingFeePercentage || 2,
                    processingFeeFixedAmount: updates.processingFeeFixedAmount || 100,
                    isActive: true,
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                res.status(201).json({
                    success: true,
                    data: newSettings,
                    message: 'Loan settings created successfully'
                });
                return;
            }
            const updatedSettings = database_1.database.updateLoanSettings(currentSettings.id, {
                ...updates,
                updatedAt: new Date()
            });
            if (!updatedSettings) {
                res.status(500).json({
                    success: false,
                    error: 'Failed to update loan settings'
                });
                return;
            }
            res.json({
                success: true,
                data: updatedSettings,
                message: 'Loan settings updated successfully'
            });
        }
        catch (error) {
            console.error('Error updating loan settings:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to update loan settings'
            });
        }
    }
    async getEligibilityCriteria(req, res) {
        try {
            const settings = database_1.database.getLoanSettings();
            if (!settings) {
                res.status(404).json({
                    success: false,
                    error: 'Loan settings not found'
                });
                return;
            }
            const criteria = {
                minLoanAmount: settings.minLoanAmount,
                maxLoanAmount: settings.maxLoanAmount,
                minTermMonths: settings.minTermMonths,
                maxTermMonths: settings.maxTermMonths,
                minCreditScore: settings.minCreditScore,
                maxDebtToIncomeRatio: settings.maxDebtToIncomeRatio,
                baseInterestRate: settings.baseInterestRate,
                maxInterestRate: settings.maxInterestRate,
                processingFeePercentage: settings.processingFeePercentage,
                processingFeeFixedAmount: settings.processingFeeFixedAmount
            };
            res.json({
                success: true,
                data: criteria
            });
        }
        catch (error) {
            console.error('Error fetching eligibility criteria:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to fetch eligibility criteria'
            });
        }
    }
    async checkEligibility(req, res) {
        try {
            const userId = req.user.id;
            const { requestedAmount, termMonths, monthlyIncome, existingDebts } = req.body;
            if (!requestedAmount || !termMonths || !monthlyIncome) {
                res.status(400).json({
                    success: false,
                    error: 'Requested amount, term, and monthly income are required'
                });
                return;
            }
            const settings = database_1.database.getLoanSettings();
            const user = database_1.database.getUserById(userId);
            if (!settings || !user) {
                res.status(404).json({
                    success: false,
                    error: 'Settings or user not found'
                });
                return;
            }
            const eligibilityCheck = {
                isEligible: true,
                reasons: [],
                recommendations: [],
                maxEligibleAmount: 0,
                suggestedTerms: []
            };
            if (requestedAmount < settings.minLoanAmount) {
                eligibilityCheck.isEligible = false;
                eligibilityCheck.reasons.push(`Minimum loan amount is $${settings.minLoanAmount}`);
            }
            if (requestedAmount > settings.maxLoanAmount) {
                eligibilityCheck.isEligible = false;
                eligibilityCheck.reasons.push(`Maximum loan amount is $${settings.maxLoanAmount}`);
            }
            if (termMonths < settings.minTermMonths) {
                eligibilityCheck.isEligible = false;
                eligibilityCheck.reasons.push(`Minimum loan term is ${settings.minTermMonths} months`);
            }
            if (termMonths > settings.maxTermMonths) {
                eligibilityCheck.isEligible = false;
                eligibilityCheck.reasons.push(`Maximum loan term is ${settings.maxTermMonths} months`);
            }
            const creditScore = user.creditScore || 650;
            if (creditScore < settings.minCreditScore) {
                eligibilityCheck.isEligible = false;
                eligibilityCheck.reasons.push(`Minimum credit score requirement is ${settings.minCreditScore}`);
                eligibilityCheck.recommendations.push('Work on improving your credit score');
            }
            const monthlyPayment = this.calculateMonthlyPayment(requestedAmount, settings.baseInterestRate, termMonths);
            const totalMonthlyDebts = (existingDebts || 0) + monthlyPayment;
            const debtToIncomeRatio = totalMonthlyDebts / monthlyIncome;
            if (debtToIncomeRatio > settings.maxDebtToIncomeRatio) {
                eligibilityCheck.isEligible = false;
                eligibilityCheck.reasons.push(`Debt-to-income ratio (${(debtToIncomeRatio * 100).toFixed(1)}%) exceeds maximum allowed (${(settings.maxDebtToIncomeRatio * 100)}%)`);
                eligibilityCheck.recommendations.push('Consider reducing existing debts or requesting a smaller loan amount');
            }
            const maxMonthlyPayment = (monthlyIncome * settings.maxDebtToIncomeRatio) - (existingDebts || 0);
            if (maxMonthlyPayment > 0) {
                eligibilityCheck.maxEligibleAmount = this.calculateMaxLoanAmount(maxMonthlyPayment, settings.baseInterestRate, termMonths);
            }
            if (!eligibilityCheck.isEligible && requestedAmount <= settings.maxLoanAmount) {
                for (let term = settings.minTermMonths; term <= settings.maxTermMonths; term += 6) {
                    const payment = this.calculateMonthlyPayment(requestedAmount, settings.baseInterestRate, term);
                    const dti = ((existingDebts || 0) + payment) / monthlyIncome;
                    if (dti <= settings.maxDebtToIncomeRatio) {
                        eligibilityCheck.suggestedTerms.push(term);
                    }
                }
            }
            if (eligibilityCheck.isEligible) {
                eligibilityCheck.recommendations.push('You meet all eligibility criteria for this loan');
            }
            res.json({
                success: true,
                data: eligibilityCheck
            });
        }
        catch (error) {
            console.error('Error checking eligibility:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to check eligibility'
            });
        }
    }
    async getInterestRate(req, res) {
        try {
            const userId = req.user.id;
            const { requestedAmount, termMonths } = req.query;
            const settings = database_1.database.getLoanSettings();
            const user = database_1.database.getUserById(userId);
            if (!settings || !user) {
                res.status(404).json({
                    success: false,
                    error: 'Settings or user not found'
                });
                return;
            }
            const creditScore = user.creditScore || 650;
            let interestRate = settings.baseInterestRate;
            if (creditScore >= 750) {
                interestRate = settings.baseInterestRate;
            }
            else if (creditScore >= 700) {
                interestRate = settings.baseInterestRate + 1;
            }
            else if (creditScore >= 650) {
                interestRate = settings.baseInterestRate + 2;
            }
            else if (creditScore >= 600) {
                interestRate = settings.baseInterestRate + 4;
            }
            else {
                interestRate = settings.baseInterestRate + 6;
            }
            interestRate = Math.min(interestRate, settings.maxInterestRate);
            const rateInfo = {
                interestRate,
                creditScore,
                riskLevel: this.getRiskLevel(creditScore),
                factors: this.getRateFactors(creditScore, settings.baseInterestRate, interestRate)
            };
            if (requestedAmount && termMonths) {
                const amount = parseFloat(requestedAmount);
                const term = parseInt(termMonths);
                const monthlyPayment = this.calculateMonthlyPayment(amount, interestRate, term);
                const totalAmount = monthlyPayment * term;
                const totalInterest = totalAmount - amount;
                rateInfo['loanDetails'] = {
                    requestedAmount: amount,
                    termMonths: term,
                    monthlyPayment: Math.round(monthlyPayment * 100) / 100,
                    totalAmount: Math.round(totalAmount * 100) / 100,
                    totalInterest: Math.round(totalInterest * 100) / 100
                };
            }
            res.json({
                success: true,
                data: rateInfo
            });
        }
        catch (error) {
            console.error('Error getting interest rate:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to get interest rate'
            });
        }
    }
    calculateMonthlyPayment(principal, annualRate, termMonths) {
        if (annualRate === 0)
            return principal / termMonths;
        const monthlyRate = annualRate / 100 / 12;
        const numerator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
        const denominator = Math.pow(1 + monthlyRate, termMonths) - 1;
        return principal * (numerator / denominator);
    }
    calculateMaxLoanAmount(maxMonthlyPayment, annualRate, termMonths) {
        if (annualRate === 0)
            return maxMonthlyPayment * termMonths;
        const monthlyRate = annualRate / 100 / 12;
        const denominator = monthlyRate * Math.pow(1 + monthlyRate, termMonths);
        const numerator = Math.pow(1 + monthlyRate, termMonths) - 1;
        return maxMonthlyPayment * (numerator / denominator);
    }
    getRiskLevel(creditScore) {
        if (creditScore >= 750)
            return 'Low';
        if (creditScore >= 700)
            return 'Medium-Low';
        if (creditScore >= 650)
            return 'Medium';
        if (creditScore >= 600)
            return 'Medium-High';
        return 'High';
    }
    getRateFactors(creditScore, baseRate, finalRate) {
        return {
            baseRate,
            creditScoreAdjustment: finalRate - baseRate,
            finalRate,
            explanation: `Rate adjusted based on credit score of ${creditScore}`
        };
    }
}
exports.LoanSettingsController = LoanSettingsController;
exports.loanSettingsController = new LoanSettingsController();
//# sourceMappingURL=loanSettingsController.js.map