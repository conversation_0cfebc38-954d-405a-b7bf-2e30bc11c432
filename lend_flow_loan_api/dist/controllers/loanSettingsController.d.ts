import { Request, Response } from 'express';
export declare class LoanSettingsController {
    getLoanSettings(req: Request, res: Response): Promise<void>;
    updateLoanSettings(req: Request, res: Response): Promise<void>;
    getEligibilityCriteria(req: Request, res: Response): Promise<void>;
    checkEligibility(req: Request, res: Response): Promise<void>;
    getInterestRate(req: Request, res: Response): Promise<void>;
    private calculateMonthlyPayment;
    private calculateMaxLoanAmount;
    private getRiskLevel;
    private getRateFactors;
}
export declare const loanSettingsController: LoanSettingsController;
//# sourceMappingURL=loanSettingsController.d.ts.map