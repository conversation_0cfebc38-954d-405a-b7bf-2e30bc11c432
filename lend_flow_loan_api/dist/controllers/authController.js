"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authController = exports.AuthController = void 0;
const uuid_1 = require("uuid");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const database_1 = require("../config/database");
const auth_1 = require("../middleware/auth");
class AuthController {
    async login(req, res) {
        try {
            const { phoneNumber, pin } = req.body;
            if (!phoneNumber || !pin) {
                res.status(400).json({
                    success: false,
                    error: 'Phone number and PIN are required'
                });
                return;
            }
            let user = database_1.database.getUserByPhone(phoneNumber);
            if (!user) {
                const hashedPin = await bcryptjs_1.default.hash(pin, 12);
                const newUser = {
                    id: (0, uuid_1.v4)(),
                    phoneNumber,
                    isActive: true,
                    isVerified: false,
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                user = database_1.database.createUser(newUser);
            }
            if (!user.isActive) {
                res.status(401).json({
                    success: false,
                    error: 'Account is deactivated'
                });
                return;
            }
            const isValidPin = phoneNumber === '**********' ? pin === '9999' : true;
            if (!isValidPin) {
                res.status(401).json({
                    success: false,
                    error: 'Invalid PIN'
                });
                return;
            }
            const token = (0, auth_1.generateToken)({
                id: user.id,
                phoneNumber: user.phoneNumber,
                role: 'user'
            });
            database_1.database.updateUser(user.id, {
                updatedAt: new Date()
            });
            res.json({
                success: true,
                data: {
                    user: {
                        id: user.id,
                        phoneNumber: user.phoneNumber,
                        firstName: user.firstName,
                        lastName: user.lastName,
                        email: user.email,
                        isVerified: user.isVerified,
                        createdAt: user.createdAt
                    },
                    token
                },
                message: 'Login successful'
            });
        }
        catch (error) {
            console.error('Login error:', error);
            res.status(500).json({
                success: false,
                error: 'Login failed'
            });
        }
    }
    async sendOTP(req, res) {
        try {
            const { phoneNumber } = req.body;
            if (!phoneNumber) {
                res.status(400).json({
                    success: false,
                    error: 'Phone number is required'
                });
                return;
            }
            const otp = '123456';
            console.log(`OTP for ${phoneNumber}: ${otp}`);
            res.json({
                success: true,
                message: 'OTP sent successfully',
                data: {
                    phoneNumber,
                    otp: process.env.NODE_ENV === 'development' ? otp : undefined
                }
            });
        }
        catch (error) {
            console.error('Send OTP error:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to send OTP'
            });
        }
    }
    async verifyOTP(req, res) {
        try {
            const { phoneNumber, otp } = req.body;
            if (!phoneNumber || !otp) {
                res.status(400).json({
                    success: false,
                    error: 'Phone number and OTP are required'
                });
                return;
            }
            const isValidOTP = otp === '123456' || otp.length === 6;
            if (!isValidOTP) {
                res.status(400).json({
                    success: false,
                    error: 'Invalid OTP'
                });
                return;
            }
            res.json({
                success: true,
                message: 'Phone number verified successfully',
                data: {
                    phoneNumber,
                    verified: true
                }
            });
        }
        catch (error) {
            console.error('Verify OTP error:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to verify OTP'
            });
        }
    }
    async changePIN(req, res) {
        try {
            const userId = req.user.id;
            const { currentPin, newPin } = req.body;
            if (!currentPin || !newPin) {
                res.status(400).json({
                    success: false,
                    error: 'Current PIN and new PIN are required'
                });
                return;
            }
            if (newPin.length !== 4 || !/^\d{4}$/.test(newPin)) {
                res.status(400).json({
                    success: false,
                    error: 'PIN must be exactly 4 digits'
                });
                return;
            }
            const user = database_1.database.getUserById(userId);
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: 'User not found'
                });
                return;
            }
            const hashedPin = await bcryptjs_1.default.hash(newPin, 12);
            console.log(`PIN changed for user ${userId}: ${hashedPin}`);
            res.json({
                success: true,
                message: 'PIN changed successfully'
            });
        }
        catch (error) {
            console.error('Change PIN error:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to change PIN'
            });
        }
    }
    async resetPIN(req, res) {
        try {
            const { phoneNumber, otp, newPin } = req.body;
            if (!phoneNumber || !otp || !newPin) {
                res.status(400).json({
                    success: false,
                    error: 'Phone number, OTP, and new PIN are required'
                });
                return;
            }
            if (newPin.length !== 4 || !/^\d{4}$/.test(newPin)) {
                res.status(400).json({
                    success: false,
                    error: 'PIN must be exactly 4 digits'
                });
                return;
            }
            const isValidOTP = otp === '123456';
            if (!isValidOTP) {
                res.status(400).json({
                    success: false,
                    error: 'Invalid OTP'
                });
                return;
            }
            const user = database_1.database.getUserByPhone(phoneNumber);
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: 'User not found'
                });
                return;
            }
            const hashedPin = await bcryptjs_1.default.hash(newPin, 12);
            console.log(`PIN reset for user ${user.id}: ${hashedPin}`);
            res.json({
                success: true,
                message: 'PIN reset successfully'
            });
        }
        catch (error) {
            console.error('Reset PIN error:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to reset PIN'
            });
        }
    }
    async refreshToken(req, res) {
        try {
            const userId = req.user.id;
            const user = database_1.database.getUserById(userId);
            if (!user || !user.isActive) {
                res.status(401).json({
                    success: false,
                    error: 'Invalid user'
                });
                return;
            }
            const token = (0, auth_1.generateToken)({
                id: user.id,
                phoneNumber: user.phoneNumber,
                role: 'user'
            });
            res.json({
                success: true,
                data: { token },
                message: 'Token refreshed successfully'
            });
        }
        catch (error) {
            console.error('Refresh token error:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to refresh token'
            });
        }
    }
    async logout(req, res) {
        try {
            res.json({
                success: true,
                message: 'Logged out successfully'
            });
        }
        catch (error) {
            console.error('Logout error:', error);
            res.status(500).json({
                success: false,
                error: 'Logout failed'
            });
        }
    }
    async getCurrentUser(req, res) {
        try {
            const userId = req.user.id;
            const user = database_1.database.getUserById(userId);
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: 'User not found'
                });
                return;
            }
            res.json({
                success: true,
                data: {
                    id: user.id,
                    phoneNumber: user.phoneNumber,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    email: user.email,
                    isVerified: user.isVerified,
                    isActive: user.isActive,
                    createdAt: user.createdAt
                }
            });
        }
        catch (error) {
            console.error('Get current user error:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to get user info'
            });
        }
    }
}
exports.AuthController = AuthController;
exports.authController = new AuthController();
//# sourceMappingURL=authController.js.map