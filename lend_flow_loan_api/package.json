{"name": "lend_flow_loan_api", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "A comprehensive RESTful API for loan management, built with Node.js, Express, and TypeScript. This API provides complete loan lifecycle management including applications, approvals, payments, and user profile management.", "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.3", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}